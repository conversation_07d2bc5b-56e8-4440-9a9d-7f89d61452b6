<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ order.id }} - Computer Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem; /* Reduced padding for print */
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        .invoice-details {
            background: #f8f9fa;
            padding: 1rem; /* Reduced padding */
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .invoice-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .total-section {
            background: #e9ecef;
            padding: 0.5rem; /* Reduced padding */
            font-weight: bold;
            font-size: 1rem; /* Slightly smaller font */
        }
        .print-btn {
            background: #28a745;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
        }
        .print-btn:hover {
            background: #218838;
        }
        @media print {
            .no-print { display: none !important; }
            .invoice-header { 
                background: #667eea !important;
                -webkit-print-color-adjust: exact; /* Ensure background prints */
                color-adjust: exact;
            }
            body {
                margin: 0.5cm; /* Reduced margins for more space */
                font-size: 12pt; /* Smaller font size for print */
            }
            .container {
                width: 100% !important;
                max-width: 100% !important;
            }
            .invoice-table, .invoice-details, .total-section, .invoice-header {
                page-break-inside: avoid; /* Prevent breaking these sections */
            }
            .row {
                page-break-inside: avoid; /* Keep rows together */
            }
            @page {
                size: A4; /* Standard page size */
                margin: 0.5cm; /* Minimal margins */
            }
            table {
                font-size: 10pt; /* Smaller table font */
            }
            h1 { font-size: 20pt; } /* Smaller header */
            h4 { font-size: 14pt; }
            p { margin-bottom: 0.3rem; } /* Tighter spacing */
        }
    </style>
</head>
<body>
    <div class="container mt-2">
        <!-- Header -->
        <div class="invoice-header text-center">
            <h1>💻 Computer Shop Invoice</h1>
            <p class="mb-0">Professional Computer Sales & Service</p>
        </div>

        <!-- Invoice Details -->
        <div class="row">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h4>📋 Invoice Details</h4>
                    <p><strong>Invoice #:</strong> {{ order.id }}</p>
                    <p><strong>Date:</strong> {{ order.order_date.strftime('%B %d, %Y') }}</p>
                    <p><strong>Time:</strong> {{ order.order_date.strftime('%I:%M %p') }}</p>
                    <p><strong>Payment Method:</strong> {{ order.payment_method or 'ACLEDA Bank QR Payment' }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-success">Paid</span></p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-details">
                    <h4>👤 Customer Information</h4>
                    <p><strong>Name:</strong> {{ customer.first_name }} {{ customer.last_name }}</p>
                    <p><strong>Email:</strong> {{ customer.email }}</p>
                    <p><strong>Phone:</strong> {{ customer.phone or 'N/A' }}</p>
                    <p><strong>Address:</strong> {{ customer.address or 'N/A' }}</p>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="invoice-table">
            <table class="table table-striped mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Product</th>
                        <th>Brand</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order_items %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ item.product_name }}</td>
                        <td>{{ item.brand }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>${{ "%.2f"|format(item.price) }}</td>
                        <td>${{ "%.2f"|format(item.quantity * item.price) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <!-- Total Section -->
            <div class="total-section text-end">
                <div class="row">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        <p class="mb-1">Subtotal: ${{ "%.2f"|format(order.total_amount) }}</p>
                        <p class="mb-1">Tax: $0.00</p>
                        <hr>
                        <h4>💰 Total Paid: ${{ "%.2f"|format(order.total_amount) }}</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="row mt-2">
            <div class="col-md-12">
                <div class="invoice-details text-center">
                    <h5>✅ Payment Confirmed</h5>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-2 no-print" style="margin: 10px;">
            <button onclick="window.print()" class="btn print-btn me-3">
                🖨️ Print Invoice
            </button>
            <a href="{{ url_for('show_dashboard') }}" class="btn btn-secondary">
                ← Homepage
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>