/* Cart Button States - Smaller Square Design */
.add-to-cart-btn {
    transition: all 0.3s ease;
    border: none;
    border-radius: 6px;
    padding: 8px;
    font-weight: 500;
    cursor: pointer;
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px;
    line-height: 1;
}

/* Active/Available Cart Button */
.add-to-cart-btn:not(:disabled) {
    background-color: #28a745 !important;
    color: #fff !important;
}

.add-to-cart-btn:not(:disabled):hover {
    background-color: #218838 !important;
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* Already in Cart Button State */
.add-to-cart-btn:disabled {
    background-color: #6c757d !important;
    color: #fff !important;
    cursor: not-allowed !important;
    opacity: 0.8;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.add-to-cart-btn:disabled:hover {
    background-color: #6c757d !important;
    transform: none;
    box-shadow: none;
}

.add-to-cart-btn:disabled i {
    font-size: 14px;
    margin: 0;
}

/* Cart Button Icons - Adjusted for Square Buttons */
.add-to-cart-btn i {
    margin: 0;
    font-size: 16px;
    line-height: 1;
}

/* Responsive Cart Buttons - Maintain Square Shape on Mobile */
@media (max-width: 768px) {
    .add-to-cart-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        min-height: 36px;
        padding: 6px;
        font-size: 12px;
        border-radius: 5px;
    }

    .add-to-cart-btn i {
        font-size: 14px;
    }

    .add-to-cart-btn:disabled {
        font-size: 10px;
    }

    .add-to-cart-btn:disabled i {
        font-size: 11px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .add-to-cart-btn {
        width: 32px;
        height: 32px;
        min-width: 32px;
        min-height: 32px;
        padding: 4px;
        font-size: 11px;
    }

    .add-to-cart-btn i {
        font-size: 12px;
    }

    .add-to-cart-btn:disabled {
        font-size: 9px;
    }
}

/* Ensure square buttons work well in card layouts */
.card .add-to-cart-btn {
    flex: 0 0 auto !important; /* Don't grow or shrink */
    margin-left: 8px; /* Space from view button */
}

/* Override any conflicting flex-fill classes */
.add-to-cart-btn.flex-fill {
    flex: 0 0 auto !important;
    width: 40px !important;
    min-width: 40px !important;
}

/* Ensure proper alignment in button containers */
.d-flex .add-to-cart-btn {
    align-self: center;
}

/* Active state for better user feedback */
.add-to-cart-btn:not(:disabled):active {
    transform: translateY(0) scale(0.98);
}