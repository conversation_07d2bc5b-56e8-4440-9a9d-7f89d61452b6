# Bakong Payment Configuration
# Copy this file to .env and fill in your real ACLEDA Bank credentials

# Your ACLEDA Bank Merchant Information
# Get these from ACLEDA Bank when you register for merchant services
BAKONG_MERCHANT_ID=your_actual_merchant_id_from_acleda
BAKONG_ACCOUNT_NUMBER=your_acleda_account_number
BAKONG_MERCHANT_NAME=Ly Heng Hab

# Optional: API credentials if using Bakong API integration
BAKONG_API_KEY=your_api_key_if_available
BAKONG_API_SECRET=your_api_secret_if_available

# Environment: sandbox for testing, production for live payments
BAKONG_ENVIRONMENT=sandbox

# Database Configuration (existing)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=12345
DB_NAME=computer_shop

# Flask Configuration (existing)
SECRET_KEY=your_secret_key_here
FLASK_ENV=development
