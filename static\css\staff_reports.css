
.main-content {
    flex: 1;
    padding: 20px 20px 60px 270px; /* Extra bottom padding for footer */
    background: #f5f7fa;
    min-height: 100vh;
}

.staff-container {
    /* max-width: 1200px; */
    margin: 0 auto;
}

.staff-widgets {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 20px;
    margin-top: 20px;
    padding: 0;
}

@media (min-width: 768px) {
    .staff-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .staff-widgets {
        grid-template-columns: repeat(3, 1fr);
    }
}

.monthly-sales-widget {
    grid-column: 1 / -1; /* Full width for monthly sales chart */
}

.widget {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 25px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: fit-content;
    min-height: auto;
}

@media (max-width: 768px) {
    .staff-widgets {
        grid-template-columns: 1fr;
        grid-gap: 15px;
    }
    .widget {
        min-height: unset;
    }
}

.widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.widget h2 {
    margin: 0;
    color: #333;
    font-size: 1.4em;
}

.widget-header .btn {
    margin: 0;
    font-size: 12px;
    padding: 6px 12px;
}

.widget-content {
    padding: 15px 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.widget-content .btn {
    align-self: flex-start;
    margin-top: auto;
}

.widget-content p {
    margin: 10px 0;
    font-size: 16px;
}

.btn {
    display: inline-block;
    background: #4aaa57;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    margin-top: 10px;
    transition: background 0.3s;
}

.btn:hover {
    background: #5ad607;
}

/* Chart container styling */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    margin: 15px 0;
}

/* Table styling */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table-bordered {
    border: 1px solid #ddd;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #ddd;
}

/* No data message styling */
.no-data-message {
    text-align: center;
    margin-top: 20px;
    display: none;
    color: #666;
    font-style: italic;
}

/* Modal styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close-button {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close-button:hover {
    color: #000;
}

/* This Month Revenue Table Styles */
.this-month-revenue-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.this-month-revenue-table th {
    background: #f8f9fa;
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-weight: bold;
    font-size: 15px;
}

.this-month-revenue-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    font-weight: 600;
    font-size: 14px;
}

.this-month-revenue-table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.this-month-revenue-table .total-row {
    background-color: #e9ecef;
    font-weight: bold;
    border-top: 2px solid #dee2e6;
    font-size: 15px;
}

.this-month-revenue-table .total-row td {
    border-bottom: none;
    font-weight: bold;
}

/* Customer History Styles */
.customer-search-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input-group input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 16px;
    min-width: 300px;
    height: 48px;
}

.search-input-group button {
    padding: 12px 20px;
    font-size: 14px;
    white-space: nowrap;
    height: 48px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-info-card {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.customer-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.customer-name {
    font-size: 18px;
    font-weight: 600;
    color: #1976d2;
}

.customer-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 14px;
    color: #666;
}

.discount-history-table {
    margin-bottom: 25px;
}

.discount-history-table h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
}

.discount-history-table table {
    font-size: 13px;
}

.discount-history-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.customer-insights {
    margin-top: 20px;
}

.insight-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.insight-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: box-shadow 0.2s ease;
}

.insight-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.insight-card:nth-child(1) .insight-icon { background: #007bff; }
.insight-card:nth-child(2) .insight-icon { background: #28a745; }
.insight-card:nth-child(3) .insight-icon { background: #ffc107; color: #333; }
.insight-card:nth-child(4) .insight-icon { background: #6f42c1; }

.insight-content {
    flex: 1;
}

.insight-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.insight-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.suggested-pricing {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
}

.suggested-pricing h5 {
    color: #856404;
    margin-bottom: 10px;
    font-size: 14px;
}

.pricing-suggestion {
    font-size: 14px;
    color: #856404;
}

.suggestion-item {
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    margin-bottom: 8px;
    border-left: 4px solid #ffc107;
}

.recent-customer-activity h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
}

.recent-customers-list {
    display: grid;
    gap: 10px;
}

.recent-customer-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recent-customer-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.recent-customer-info {
    flex: 1;
}

.recent-customer-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.recent-customer-details {
    font-size: 12px;
    color: #6c757d;
}

.recent-customer-stats {
    text-align: right;
    font-size: 12px;
    color: #6c757d;
}

.no-results-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-results-message i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-results-message p {
    margin: 0;
    font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .search-input-group input {
        min-width: auto;
        width: 100%;
    }

    .search-input-group button {
        width: 100%;
        justify-content: center;
    }

    .customer-search-section {
        padding: 16px;
    }

    .customer-info-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .insight-cards {
        grid-template-columns: 1fr;
    }
}