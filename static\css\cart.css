       body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        .products, .cart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .products {
            flex: 2;
        }
        .cart {
            flex: 1;
        }
        .product {
            border-bottom: 1px solid #ddd;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .product:last-child {
            border-bottom: none;
        }
        .product button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .product button:hover {
            background: #218838;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .cart-item button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .cart-item button:hover {
            background: #c82333;
        }
        h2 {
            margin-top: 0;
        }
        #cart-count {
            font-weight: bold;
        }
        #buy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px;
            width: 48%;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
        }
        #buy-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        #buy-button:hover:not(:disabled) {
            background: #0056b3;
        }
        #back-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px;
            width: 48%;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        #back-button:hover {
            background: #5a6268;
        }
        #cart-total {
            font-weight: bold;
            margin-top: 10px;
            text-align: right;
        }
        .cart-buttons {
            display: flex;
            justify-content: space-between;
        }















.footer1 {
    background-color: #24262b;
    padding: 70px 0;
    color: white;
    
}

.container1 {
    max-width: 75%;
    margin: auto;
    
}

.row1 {
    display: flex;
    flex-wrap: wrap;
    
}

.footer1-col {
    width: 20%;
    padding: 0 35px;
    
}

.footer1-col h4 {
    font-size: 18px;
    text-transform: capitalize;
    margin-bottom: 35px;
    font-weight: 500;
    position: relative;
    
}

.footer1-col h4::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    background-color: #e91e63;
    height: 2px;
    width: 50px;
    
}

.footer1-col ul li {
    margin-bottom: 10px;
    margin-left: -35px;
    padding-left: 10px;
    list-style: none;
    
    /* background-color: #0d6efd; */
}

.footer1-col ul li a {
    font-size: 16px;
    text-transform: capitalize;
    color: #bbbbbb;
    text-decoration: none;
    font-weight: 300;
    display: block;
    transition: all 0.3s ease;
    
    
}

body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    
}

header, .top-bar, nav, .hero-container, .categories {
    width: 100%;
    text-align: center;
}

.top-bar {
    background-color: #333;
    color: white;
    padding: 10px 0;
}

.top-bar span {
    font-size: 2em;
}

nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

nav ul li {
    position: relative;
    margin: 0 15px;
    padding: 10px 0;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

nav ul li a:hover, nav ul li a:focus {
    background-color: #f0f0f0;
    color: #333;
    outline: none;
}

@keyframes slideDown {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

nav ul li .dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 200px;
    padding: 15px 0;
    animation: slideDown 0.3s ease-in-out;
}

nav ul li .dropdown li {
    margin: 0;
    padding: 0;
}

nav ul li .dropdown li a {
    display: block;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    transition: background-color 0.3s;
}

nav ul li .dropdown li a:hover, nav ul li .dropdown li a:focus {
    background-color: #444;
    color: #fff;
}

nav ul li:hover .dropdown, nav ul li:focus-within .dropdown {
    display: block;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    cursor: pointer;
    transition: transform 0.3s, filter 0.3s;
}

.search-icon:focus {
    outline: 2px solid #e67e22;
    outline-offset: 2px;
}

.search-icon img:hover, .search-icon img:focus {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.search-input {
    width: 0;
    padding: 0;
    border: none;
    outline: none;
    background-color: white;
    color: black;
    font-size: 16px;
    transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
    position: absolute;
    left: 30px;
    opacity: 0;
    pointer-events: none;
    border-radius: 50px;
}

.search-input:focus {
    border: 1px solid #e67e22;
    outline: none;
}

.search-container:hover .search-input, .search-container.active .search-input {
    width: 200px;
    padding: 5px 10px;
    opacity: 1;
    pointer-events: auto;
    border: 1px solid #e67e22;
}

.hero-container {
    position: relative;
    width: 90%;
    max-width: 1200px;
    height: 500px;
    overflow: hidden;
    margin: 20px auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
}

.hero-section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(to right, gray, #202020);
    border-radius: 20px;
}

.hero-section.active {
    display: flex;
}

.hero-section.animate-in {
    animation: slideIn 1s forwards;
}

@keyframes slideIn {
    0% { transform: translateX(100%); }
    100% { transform: translateX(0); }
}

.navigation-dots {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.dot {
    height: 15px;
    width: 15px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dot.active {
    background-color: #717171;
}

.hero-image {
    max-width: 40%;
    height: auto;
    margin-right: 20px;
}

.hero-details {
    text-align: left;
}

.hero-section h1 {
    font-size: 2.5em;
    margin: 10px 0;
    color: white;
}

.hero-section .subheading {
    font-size: 1.2em;
    margin: 5px 0;
    color: #ccc;
}

.hero-section h2 {
    font-size: 1.5em;
    color: #e67e22;
    margin: 10px 0;
}

.hero-section button {
    padding: 10px 20px;
    font-size: 1em;
    background-color: #e67e22;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.hero-section button:hover, .hero-section button:focus {
    background-color: #d35400;
    transform: scale(1.05);
    outline: none;
}

.categories {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.category {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
    padding: 35px;
    border: 3px solid #ccc;
    border-radius: 10px;
    transition: transform 0.3s, background-color 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* background-color: #0d6efd; */
}

.category:hover, .category:focus-within {
    transform: scale(1.05);
    background-color: #f0f0f0;
}

.category-icon {
    max-width: 90px;
    height: auto;
}

.content.blur {
    filter: blur(2px);
    transition: filter 0.3s ease;
}

.footer1 {
    background-color: #24262b;
    padding: 70px 0;
    color: white;
    
}

.container1 {
    max-width: 75%;
    margin: auto;
    
}

.row1 {
    display: flex;
    flex-wrap: wrap;
    
}

.footer1-col {
    width: 20%;
    padding: 0 35px;
    
}

.footer1-col h4 {
    font-size: 18px;
    text-transform: capitalize;
    margin-bottom: 35px;
    font-weight: 500;
    position: relative;
    
}

.footer1-col h4::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    background-color: #e91e63;
    height: 2px;
    width: 50px;
    
}

.footer1-col ul li {
    margin-bottom: 10px;
    margin-left: -35px;
    padding-left: 10px;
    list-style: none;
    
    /* background-color: #0d6efd; */
}

.footer1-col ul li a {
    font-size: 16px;
    text-transform: capitalize;
    color: #bbbbbb;
    text-decoration: none;
    font-weight: 300;
    display: block;
    transition: all 0.3s ease;
    
    
}

.footer1-col ul li a:hover, .footer-col ul li a:focus {
    color: #ffffff;
    padding-left: 8px;
}

.footer1-col .social-links a {
    display: inline-block;
    height: 40px;
    width: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0 10px 10px 0;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    color: #ffffff;
    transition: all 0.5s ease;
}

.footer1-col .social-links a:hover, .footer-col .social-links a:focus {
    color: #24262b;
    background-color: #ffffff;
}

.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 400px !important; /* Ensure minimum height for consistent layout */
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 200px !important;
    object-fit: contain;
    padding: 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    flex-shrink: 0 !important; /* Prevent image from shrinking */
}

.card-body {
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important; /* Take up remaining space */
    padding: 1rem !important;
    height: 100% !important;
    position: relative;
    padding-bottom: 4rem !important; /* Make room for buttons */
}

.card-text {
    flex-grow: 1 !important;
    color: #6c757d;
    margin-bottom: 1rem !important; /* Ensure space before buttons */
}

.price {
    color: #0d6efd;
    font-size: 1.1rem;
    font-weight: bold;
}

.add-to-cart {
    transition: all 0.3s;
}

.add-to-cart:hover {
    background-color: #0b5ed7 !important;
    color: white !important;
}

/* Button container for consistent positioning */
.card .d-flex {
    margin-top: auto !important; /* Push buttons to bottom of card */
}

/* Legacy support for d-grid containers (if any remain) */
.card .d-grid {
    margin-top: auto !important; /* Push buttons to bottom of card */
    position: absolute !important;
    bottom: 1rem !important;
    left: 1rem !important;
    right: 1rem !important;
}

/* Ensure view product buttons are properly positioned */
.view-product-btn {
    width: 100% !important;
    margin-top: auto !important;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-container {
        height: 400px;
    }
    .hero-image {
        max-width: 50%;
    }
    .category {
        width: 120px;
    }
    nav ul {
        flex-direction: column;
        align-items: flex-start;
    }
    nav ul li {
        margin: 5px 0;
    }
    .search-container:hover .search-input, .search-container.active .search-input {
        width: 150px;
    }
    .footer1-col {
        width: 50%;
        margin-bottom: 30px;
    }
}

@media (max-width: 576px) {
    .hero-container {
        height: 300px;
    }
    .hero-section h1 {
        font-size: 1.8em;
    }
    .hero-section .subheading {
        font-size: 1em;
    }
    .hero-section h2 {
        font-size: 1.2em;
    }
    .category {
        width: 100px;
    }
    .footer1-col {
        width: 100%;
    }
}