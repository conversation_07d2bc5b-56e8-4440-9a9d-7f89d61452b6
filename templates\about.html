<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Gold One Computer</title>
    <link rel="stylesheet" href="/static/css/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        header {
            text-align: center;
            padding: 20px 0;
        }
        h1, h2 {
            color: #333;
        }
        section {
            margin-bottom: 30px;
        }
        ul {
            list-style-type: disc;
            padding-left: 20px;
        }
        .contact-info {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .top-bar {
            background-color: #333;
            color: white;
            padding: 10px 0;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
        }
        .top-bar span {
            font-size: 2em;
            display: block;
            text-align: center;
        }
        nav ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
        }
        nav ul li {
            position: relative;
            margin: 0 15px;
            padding: 10px 0;
        }
        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s, color 0.3s;
        }
        nav ul li a:hover, nav ul li a:focus {
            background-color: #f0f0f0;
            color: #333;
            outline: none;
        }
        @keyframes slideDown {
            0% { opacity: 0; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        nav ul li .dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #333;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            min-width: 200px;
            padding: 15px 0;
            animation: slideDown 0.3s ease-in-out;
        }
        nav ul li .dropdown li {
            margin: 0;
            padding: 0;
        }
        nav ul li .dropdown li a {
            display: block;
            padding: 12px 20px;
            color: white;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        nav ul li .dropdown li a:hover, nav ul li .dropdown li a:focus {
            background-color: #444;
            color: #fff;
        }
        nav ul li:hover .dropdown, nav ul li:focus-within .dropdown {
            display: block;
        }
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        .search-icon {
            cursor: pointer;
            transition: transform 0.3s, filter 0.3s;
            background: none;
            border: none;
            padding: 0;
        }
        .search-icon:focus {
            outline: 2px solid #e67e22;
            outline-offset: 2px;
        }
        .search-icon img:hover, .search-icon img:focus {
            transform: scale(1.1);
            filter: brightness(1.2);
        }
        .search-input {
            width: 0;
            padding: 0;
            border: none;
            outline: none;
            background-color: white;
            color: black;
            font-size: 16px;
            transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
            position: absolute;
            left: 30px;
            opacity: 0;
            pointer-events: none;
            border-radius: 50px;
        }
        .search-input:focus {
            border: 1px solid #e67e22;
            outline: none;
        }
        .search-container.active .search-input {
            width: 200px;
            padding: 5px 10px;
            opacity: 1;
            pointer-events: auto;
            border: 1px solid #e67e22;
        }
        .search-suggestions {
            position: absolute;
            background-color: #ffffff;
            border: 1px solid #555;
            border-top: none;
            width: 200px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            cursor: pointer;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            top: 100%;
            left: 30px;
            margin-top: 5px;
        }
        .search-suggestions div {
            color: #000000;
            background-color: #ffffff;
            padding: 8px 12px;
            border-bottom: 1px solid #333;
            transition: all 0.2s ease;
        }
        .search-suggestions div:hover {
            background-color: #0c0c0c;
            color: #ffffff;
        }
        main {
            margin-top: 120px; /* Offset for fixed top-bar */
        }
    </style>
</head>
<body>
     <header>
        <div class="top-bar">
            <span>Russeykeo Computer</span>
            <nav>
                
                <ul>
                    <li><a href="{{ url_for('show_dashboard') }}">Home</a></li>
                   
                    <li><a href="#">Categories</a>
                        <ul class="dropdown">
                            <li><a href="/products/category/multi/1,5">Laptops</a></li>
                            <li><a href="/products/category/2">Desktops</a></li>
                            <li><a href="/products/category/3">Accessories</a></li>
                        </ul>
                    </li>
                   <li><a href="#">Products</a>
                        <ul class="dropdown">
                            <li><a href="/products/brand/dell">Dell</a></li>
                            <li><a href="/products/brand/hp">HP</a></li>
                            <li><a href="/products/brand/lenovo">Lenovo</a></li>
                            <li><a href="/products/brand/asus">Asus</a></li>
                            <li><a href="/products/brand/acer">Acer</a></li>
                            <li><a href="/products/brand/razer">Razer</a></li>
                        </ul>
                    </li>
                    <li><a href="#">About Company</a>
                        <ul class="dropdown">
                            <li><a href="{{ url_for('about') }}">About Us</a></li>
                            <li><a href="{{ url_for('services') }}">Our Services</a></li>
                            <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li>
                            
                        </ul>
                    </li>
                     <li><a href="{{ url_for('cart') }}">My Cart</a></li>
                     {% if not session.username %}
                     <li><a href="{{ url_for('auth.register') }}">Create Account</a></li>
                     {% endif %}
                    {% if session.username %}
                        {% if session.role == 'customer' %}
                        <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
                        <li><a href="#" onclick="showNotifications()" id="notifications-link">Notifications <span id="notification-badge" class="notification-badge" style="display: none;">0</span></a></li>
                        {% elif session.role in ['staff', 'admin', 'super_admin'] %}
                        <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
                        <!-- <li><a href="#" onclick="showNotifications()" id="notifications-link">Notifications <span id="notification-badge" class="notification-badge" style="display: none;">0</span></a></li> -->
                        <!-- <li><a href="{{ url_for('auth.staff_dashboard') }}">Staff Dashboard</a></li> -->
                        {% endif %}
                    {% endif %}
                    <!-- <li><a href="#">Check Out</a></li> -->
                    <!-- User Authentication Section -->
                    {% if session.username %}
                    <!-- Logged in user - show username and logout -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-2"></i>
                            <span class="username-text">{{ session.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                           
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    {% else %}
                    <!-- Not logged in - show login button -->
                    <li class="nav-item">
                        <a href="/auth/login" class="btn btn-outline-light btn-sm d-flex align-items-center">
                            <i class="bi bi-person-plus me-2"></i>
                            Login Account
                        </a>
                    </li>
                    {% endif %}
                    <li>
                    <form class="search-container" action="/search" method="GET" onsubmit="return validateSearch()">
                        <button type="button" class="search-icon" tabindex="0" aria-label="Search products" style="background:none; border:none; padding:0; cursor:pointer;">
                            <img src="/static/icons/search.png" alt="Search Icon" style="height: 20px;">
                        </button>
                        <input type="text" name="q" class="search-input" placeholder="Search..." aria-label="Search products" required>
                        <div class="search-suggestions"></div>
                    </form>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <header>
            <h1>About Us</h1>
        </header>

        <section>
            <p>Russeykeo Computer is a one-stop computer shop located in the town center of Phnom Penh. With over a decade of experience in supplying IT products, we are committed to providing the best service and quality products to our customers at competitive prices.</p>
        </section>

        <section>
            <h2>Products & Services</h2>
            <h3>Products</h3>
            <ul>
                <li>Laptop & Desktop Computers</li>
                <li>Gaming Gear</li>
                <li>Computer Accessories & Spare Parts</li>
                <li>And more...</li>
            </ul>

            <h3>Services</h3>
            <ul>
                <li>Install Windows and Software</li>
                <li>Repair computer, printer, and other hardware</li>
                <li>Other computer-related services</li>
            </ul>
        </section>

        <section class="contact-info">
            <h2>Contact Us</h2>
            <p>Phone-call or Telegram: <a href="tel:+85515433830">015 433 830</a></p>
            <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </section>
    </main>

    <script>
        // Toggle search bar visibility
        const searchContainer = document.querySelector('.search-container');
        const searchIcon = document.querySelector('.search-icon');
        const searchInput = document.querySelector('.search-input');

        searchIcon.addEventListener('click', () => {
            searchContainer.classList.toggle('active');
            if (searchContainer.classList.contains('active')) {
                searchInput.focus();
            }
        });

        // Placeholder for notification functionality
        function showNotifications() {
            alert('Notifications feature is not implemented in this static version.');
        }
    </script>
    
</body>
</html>