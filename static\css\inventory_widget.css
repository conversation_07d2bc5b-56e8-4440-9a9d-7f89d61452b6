
.widget {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: #fff;
    max-width: 7000px;
    margin: 0px -20px 0px 20px;
   
}

.brand-panel-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    max-height: 210px;
    overflow-y: auto;
    padding: 8px 0;
}

.brand-btn {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
    color: #333;
    transition: background-color 0.3s ease;
    white-space: nowrap;
    max-width: 130px;
}

.brand-btn:hover {
    background-color: #e0e0e0;
}

.brand-btn .brand-icon {
    margin-right: 6px;
    font-size: 0.9em;
    color: #333;
}

.brand-btn .brand-name-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow: hidden;
    text-overflow: ellipsis;
}

.brand-btn .brand-name-line1,
.brand-btn .brand-name-line2 {
    max-width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.brand-btn .product-count {
    font-weight: bold;
    font-size: 0.85em;
    min-width: 30px;
    color: #333;
}

.button-group {
    display: flex;
    justify-content: center; /* Center the single button */
    margin-top: 8px;
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.85em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.manage-btn {
    background-color: #4CAF50;
}

.manage-btn:hover {
    background-color: #45a049;
}