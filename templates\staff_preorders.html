{% extends "base.html" %}

{% block title %}Pre-Orders Management{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .status-badge {
        font-size: 0.85em;
    }
    .preorder-row:hover {
        background-color: #f8f9fa;
    }
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
    }

    /* Mobile preorder card styling */
    .mobile-preorder-card {
        display: none;
        background: #fff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .mobile-preorder-card:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .mobile-preorder-card .preorder-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .mobile-preorder-card .preorder-product {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .mobile-preorder-card .preorder-product img {
        flex-shrink: 0;
    }

    .mobile-preorder-card .preorder-details {
        margin-bottom: 15px;
    }

    .mobile-preorder-card .preorder-details p {
        margin: 5px 0;
    }

    .mobile-preorder-card .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }

    .mobile-preorder-card .action-buttons .btn {
        flex: 1;
        min-width: 100px;
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .staff-container {
            padding: 10px;
        }

        .table-responsive table {
            display: none;
        }

        #mobile-preorders-list {
            display: block !important;
        }

        .mobile-preorder-card {
            display: block;
        }

        .status-filter-container {
            margin-bottom: 15px;
        }

        .status-filter-container select {
            width: 100%;
            padding: 10px;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-clock text-warning"></i> Pre-Orders Management</h2>
                    <div class="d-flex gap-2 align-items-center">
                        <label for="status-filter" class="form-label mb-0 me-2">
                            <i class="bi bi-filter"></i> Filter by Status:
                        </label>
                        <select name="status" id="status-filter" class="form-select" style="width: auto; min-width: 150px;">
                            <option value="">All Statuses</option>
                            <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="confirmed" {% if current_status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                            <option value="partially_paid" {% if current_status == 'partially_paid' %}selected{% endif %}>Partially Paid</option>
                            <option value="ready_for_pickup" {% if current_status == 'ready_for_pickup' %}selected{% endif %}>Ready for Pickup</option>
                            <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                            <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                </div>

                {% if current_status %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Showing pre-orders with status: <strong>{{ current_status.title() }}</strong>
                    <a href="{{ url_for('staff_preorders') }}" class="btn btn-sm btn-primary ms-2" style="padding: 6px 12px; font-size: 0.9rem;">Show All</a>
                </div>
                {% endif %}

                {% if pre_orders %}
                <div class="card" id="preorders-container">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table style="width: 100%; border-collapse: collapse; border: 1px solid #aaa; font-weight: 400;" class="mb-0">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Pre-Order #</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Customer</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Product</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Quantity</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Expected Price</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Deposit</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Status</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Created Date</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for preorder in pre_orders %}
                                    <tr class="preorder-row">
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            <strong>#{{ preorder.id }}</strong>
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            <div>
                                                <strong>{{ preorder.first_name }} {{ preorder.last_name }}</strong>
                                            </div>
                                            <small class="text-muted">{{ preorder.email }}</small><br>
                                            <small class="text-muted">{{ preorder.phone }}</small>
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            <div class="d-flex align-items-center">
                                                <img src="/static/uploads/products/{{ preorder.product_photo }}"
                                                     alt="{{ preorder.product_name }}"
                                                     class="product-image me-2">
                                                <div>
                                                    <strong>{{ preorder.product_name }}</strong><br>
                                                    <small class="text-muted">Current Stock: {{ preorder.current_stock }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">{{ preorder.quantity }}</td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">${{ "%.2f"|format(preorder.expected_price * preorder.quantity) }}</td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            {% if preorder.deposit_amount and preorder.deposit_amount > 0 %}
                                                <span class="text-success">${{ "%.2f"|format(preorder.deposit_amount) }}</span><br>
                                                <small class="text-muted">{{ preorder.deposit_payment_method }}</small>
                                            {% else %}
                                                <span class="text-muted">No deposit</span>
                                            {% endif %}
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            {% if preorder.status == 'pending' %}
                                                <span class="badge bg-warning text-dark status-badge">
                                                    <i class="bi bi-hourglass-split"></i> Pending
                                                </span>
                                            {% elif preorder.status == 'confirmed' %}
                                                <span class="badge bg-info status-badge">
                                                    <i class="bi bi-check-circle"></i> Confirmed
                                                </span>
                                            {% elif preorder.status == 'partially_paid' %}
                                                <span class="badge bg-primary status-badge">
                                                    <i class="bi bi-credit-card"></i> Partially Paid
                                                </span>
                                            {% elif preorder.status == 'ready_for_pickup' %}
                                                <span class="badge bg-success status-badge">
                                                    <i class="bi bi-box-seam"></i> Ready
                                                </span>
                                            {% elif preorder.status == 'completed' %}
                                                <span class="badge bg-success status-badge">
                                                    <i class="bi bi-check-all"></i> Completed
                                                </span>
                                            {% elif preorder.status == 'cancelled' %}
                                                <span class="badge bg-danger status-badge">
                                                    <i class="bi bi-x-circle"></i> Cancelled
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            <div>{{ preorder.created_date.strftime('%m/%d/%Y') }}</div>
                                            <small class="text-muted">{{ preorder.created_date.strftime('%I:%M %p') }}</small>
                                        </td>
                                        <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                                            <div class="btn-group-vertical btn-group-sm">
                                                <!-- Always show View Details button first -->
                                                <button type="button" class="btn btn-info btn-sm"
                                                        onclick="showPreOrderDetails('{{ preorder.id }}')"
                                                        title="View pre-order details"
                                                        aria-label="View details for pre-order {{ preorder.id }}"
                                                        style="padding: 6px 12px; font-size: 0.9rem;">
                                                    <i class="bi bi-eye"></i> View Details
                                                </button>

                                                {% if preorder.status == 'pending' %}
                                                    <button type="button" class="btn btn-success btn-sm"
                                                            onclick="updateStatus('{{ preorder.id }}', 'confirmed')"
                                                            title="Confirm pre-order"
                                                            aria-label="Confirm pre-order for {{ preorder.product_name }}"
                                                            style="padding: 6px 12px; font-size: 0.9rem;">
                                                        <i class="bi bi-check"></i> Confirm
                                                    </button>
                                                {% elif preorder.status == 'confirmed' %}
                                                    <button type="button" class="btn btn-primary btn-sm"
                                                            onclick="completePreOrder('{{ preorder.id }}')"
                                                            title="Complete pre-order and notify customer (stock validation will be performed)"
                                                            aria-label="Complete pre-order for {{ preorder.product_name }}"
                                                            style="padding: 6px 12px; font-size: 0.9rem;">
                                                        <i class="bi bi-check-all"></i> Complete
                                                    </button>
                                                {% elif preorder.status in ['confirmed', 'partially_paid'] and preorder.current_stock > 0 %}
                                                    <button type="button" class="btn btn-primary btn-sm"
                                                            onclick="markReady('{{ preorder.id }}')"
                                                            title="Mark as ready for pickup"
                                                            aria-label="Mark pre-order ready for pickup for {{ preorder.product_name }}"
                                                            style="padding: 6px 12px; font-size: 0.9rem;">
                                                        <i class="bi bi-box-seam"></i> Mark Ready
                                                    </button>
                                                {% endif %}

                                                {% if preorder.status not in ['completed', 'cancelled'] %}
                                                    <button type="button" class="btn btn-secondary btn-sm"
                                                            onclick="showUpdateModal('{{ preorder.id }}', '{{ preorder.status }}')"
                                                            title="Update pre-order status"
                                                            aria-label="Update status for pre-order {{ preorder.id }}"
                                                            style="padding: 6px 12px; font-size: 0.9rem;">
                                                        <i class="bi bi-pencil"></i> Update
                                                    </button>
                                                {% endif %}

                                                {% if preorder.status not in ['ready_for_pickup', 'confirmed'] %}
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                            onclick="deletePreOrder('{{ preorder.id }}')"
                                                            title="Delete pre-order"
                                                            aria-label="Delete pre-order {{ preorder.id }}"
                                                            style="padding: 6px 12px; font-size: 0.9rem;">
                                                        <i class="bi bi-trash"></i> Delete
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div id="mobile-preorders-list"></div>
                    </div>
                </div>

                <!-- Pagination -->
                <div id="pagination-container">
                {% if pagination.total_pages > 1 %}
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if pagination.page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('staff_preorders', page=pagination.page-1, status=current_status) }}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in range(1, pagination.total_pages + 1) %}
                            {% if page_num == pagination.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('staff_preorders', page=page_num, status=current_status) }}">{{ page_num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('staff_preorders', page=pagination.page+1, status=current_status) }}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-clock display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">No Pre-Orders Found</h4>
                    <p class="text-muted">
                        {% if current_status %}
                            No pre-orders found with status "{{ current_status }}".
                        {% else %}
                            No pre-orders have been placed yet.
                        {% endif %}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Pre-Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="updateStatusForm">
                        <div class="mb-3">
                            <label for="status-select" class="form-label">New Status</label>
                            <select class="form-select" id="status-select" required>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="partially_paid">Partially Paid</option>
                                <option value="ready_for_pickup">Ready for Pickup</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="status-notes" class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" id="status-notes" rows="3" placeholder="Add any notes about this status change..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirm-update">Update Status</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Container for Notifications -->
    <div id="message-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="{{ url_for('static', filename='js/item-counter.js') }}"></script>
    <script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
    <script src="{{ url_for('static', filename='js/staff_preorders_pagination.js') }}"></script>
    <script>
        let currentPreOrderId = null;

        function updateStatus(preOrderId, status) {
            updatePreOrderStatus(preOrderId, status);
        }

        async function markReady(preOrderId) {
            const confirmed = await showConfirmation('Mark Ready for Pickup', 'Mark this pre-order as ready for pickup? The customer will be notified.');
            if (confirmed) {
                fetch(`/api/staff/preorders/${preOrderId}/mark-ready`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('Pre-order marked as ready for pickup!', 'success');
                        location.reload();
                    } else {
                        showMessage('Error: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('An error occurred while updating the pre-order.', 'error');
                });
            }
        }

        function completePreOrder(preOrderId) {
                fetch(`/api/staff/preorders/${preOrderId}/complete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('✅ Pre-order completed and customer notified!', 'success');
                    } else if (data.stock_issue) {
                        showMessage('⚠️ Product is out of stock. Admin has been notified. Pre-order remains confirmed until restocked.', 'warning');
                    } else {
                        showMessage('❌ Error: ' + data.error, 'error');
                    }
                    // Always reload after any action
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('❌ An error occurred while completing the pre-order.', 'error');
                });
        }

        function showUpdateModal(preOrderId, currentStatus) {
            currentPreOrderId = preOrderId;
            document.getElementById('status-select').value = currentStatus;
            document.getElementById('status-notes').value = '';
            
            const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
            modal.show();
        }

        document.getElementById('confirm-update').addEventListener('click', function() {
            if (!currentPreOrderId) return;

            const status = document.getElementById('status-select').value;
            const notes = document.getElementById('status-notes').value;

            updatePreOrderStatus(currentPreOrderId, status, notes);
        });

        function updatePreOrderStatus(preOrderId, status, notes = null) {
            fetch(`/api/staff/preorders/${preOrderId}/update-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: status,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal if open
                    const modal = bootstrap.Modal.getInstance(document.getElementById('updateStatusModal'));
                    if (modal) modal.hide();

                    showMessage(data.message, 'success');
                    location.reload();
                } else {
                    showMessage('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('An error occurred while updating the pre-order.', 'error');
            });
        }

        async function deletePreOrder(preOrderId) {
            const confirmed = await showDeleteConfirmation('Delete Pre-Order', 'Are you sure you want to permanently delete this pre-order? This action cannot be undone.');
            if (confirmed) {
                fetch(`/api/staff/preorders/${preOrderId}/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('Pre-order deleted successfully!', 'success');
                        location.reload();
                    } else {
                        showMessage('Error deleting pre-order: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Error deleting pre-order', 'error');
                });
            }
        }

        function showPreOrderDetails(preorderId) {
            // Fetch pre-order details
            fetch(`/auth/staff/api/pre_order/${preorderId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPreOrderDetailsModal(data.pre_order);
                    } else {
                        showMessage('Error loading pre-order details: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Error loading pre-order details', 'error');
                });
        }

        function displayPreOrderDetailsModal(preorder) {
            // Create modal HTML
            const modalHtml = `
                <div id="preorderDetailsModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0; color: #333;">Pre-Order #${preorder.id} Details</h3>
                            <span onclick="closePreOrderDetailsModal()" style="font-size: 28px; font-weight: bold; cursor: pointer; color: #aaa;">&times;</span>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div>
                                <strong>Customer:</strong><br>
                                ${preorder.first_name} ${preorder.last_name}<br>
                                <small style="color: #666;">${preorder.email}</small><br>
                                <small style="color: #666;">${preorder.phone || 'No phone'}</small>
                            </div>
                            <div>
                                <strong>Status:</strong><br>
                                <span style="background-color: ${getStatusColor(preorder.status)}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                    ${preorder.status.charAt(0).toUpperCase() + preorder.status.slice(1)}
                                </span>
                            </div>
                        </div>

                        <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <img src="/static/uploads/products/${preorder.product_photo}" alt="${preorder.product_name}"
                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px;">
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 5px 0;">${preorder.product_name}</h4>
                                    <p style="margin: 0; color: #666;">Quantity: ${preorder.quantity}</p>
                                    <p style="margin: 0; color: #666;">Expected Price: $${parseFloat(preorder.expected_price || 0).toFixed(2)}</p>
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div>
                                <strong>Created Date:</strong><br>
                                ${new Date(preorder.created_date).toLocaleDateString()} ${new Date(preorder.created_date).toLocaleTimeString()}
                            </div>
                            <div>
                                <strong>Expected Availability:</strong><br>
                                ${preorder.expected_availability_date ? new Date(preorder.expected_availability_date).toLocaleDateString() : 'Not specified'}
                            </div>
                        </div>

                        ${preorder.notes ? `
                            <div style="margin-bottom: 20px;">
                                <strong>Notes:</strong><br>
                                <div style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 4px solid #007bff;">
                                    ${preorder.notes}
                                </div>
                            </div>
                        ` : ''}

                        <div style="text-align: right;">
                            <button onclick="closePreOrderDetailsModal()" style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closePreOrderDetailsModal() {
            const modal = document.getElementById('preorderDetailsModal');
            if (modal) {
                modal.remove();
            }
        }

        function getStatusColor(status) {
            const colors = {
                'pending': '#ffc107',
                'confirmed': '#17a2b8',
                'ready': '#28a745',
                'completed': '#6c757d',
                'cancelled': '#dc3545'
            };
            return colors[status] || '#6c757d';
        }

        // Professional confirmation modal for actions
        function showConfirmation(title, message) {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'confirmation-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(2px);
                `;

                // Create modal content
                const modal = document.createElement('div');
                modal.className = 'confirmation-modal';
                modal.style.cssText = `
                    background: white;
                    border-radius: 12px;
                    padding: 0;
                    max-width: 420px;
                    width: 90%;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    transform: scale(0.9);
                    transition: transform 0.2s ease;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;

                modal.innerHTML = `
                    <div style="padding: 24px 24px 16px 24px; text-align: center;">
                        <div style="width: 64px; height: 64px; background: #dbeafe; border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                            <svg width="32" height="32" fill="#2563eb" viewBox="0 0 24 24">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: #111827;">${title}</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px; line-height: 1.5;">${message}</p>
                    </div>
                    <div style="padding: 16px 24px 24px 24px; display: flex; gap: 12px; justify-content: center;">
                        <button class="cancel-btn" style="
                            background: #f3f4f6;
                            color: #374151;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Cancel</button>
                        <button class="confirm-btn" style="
                            background: #2563eb;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Confirm</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Animate in
                requestAnimationFrame(() => {
                    modal.style.transform = 'scale(1)';
                });

                // Add hover effects
                const cancelBtn = modal.querySelector('.cancel-btn');
                const confirmBtn = modal.querySelector('.confirm-btn');

                cancelBtn.addEventListener('mouseenter', () => {
                    cancelBtn.style.backgroundColor = '#e5e7eb';
                });
                cancelBtn.addEventListener('mouseleave', () => {
                    cancelBtn.style.backgroundColor = '#f3f4f6';
                });

                confirmBtn.addEventListener('mouseenter', () => {
                    confirmBtn.style.backgroundColor = '#1d4ed8';
                });
                confirmBtn.addEventListener('mouseleave', () => {
                    confirmBtn.style.backgroundColor = '#2563eb';
                });

                // Handle button clicks
                const cleanup = () => {
                    modal.style.transform = 'scale(0.9)';
                    overlay.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 200);
                };

                cancelBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(false);
                });

                confirmBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(true);
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        cleanup();
                        resolve(false);
                    }
                });

                // Close on Escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        cleanup();
                        resolve(false);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }

        // Professional delete confirmation modal
        function showDeleteConfirmation(title, message) {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'delete-confirmation-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(2px);
                `;

                // Create modal content
                const modal = document.createElement('div');
                modal.className = 'delete-confirmation-modal';
                modal.style.cssText = `
                    background: white;
                    border-radius: 12px;
                    padding: 0;
                    max-width: 420px;
                    width: 90%;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    transform: scale(0.9);
                    transition: transform 0.2s ease;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;

                modal.innerHTML = `
                    <div style="padding: 24px 24px 16px 24px; text-align: center;">
                        <div style="width: 64px; height: 64px; background: #fee2e2; border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                            <svg width="32" height="32" fill="#dc2626" viewBox="0 0 24 24">
                                <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                            </svg>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: #111827;">${title}</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px; line-height: 1.5;">${message}</p>
                    </div>
                    <div style="padding: 16px 24px 24px 24px; display: flex; gap: 12px; justify-content: center;">
                        <button class="cancel-btn" style="
                            background: #f3f4f6;
                            color: #374151;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Cancel</button>
                        <button class="delete-btn" style="
                            background: #dc2626;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            font-size: 14px;
                            transition: background-color 0.2s;
                            min-width: 80px;
                        ">Delete</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Animate in
                requestAnimationFrame(() => {
                    modal.style.transform = 'scale(1)';
                });

                // Add hover effects
                const cancelBtn = modal.querySelector('.cancel-btn');
                const deleteBtn = modal.querySelector('.delete-btn');

                cancelBtn.addEventListener('mouseenter', () => {
                    cancelBtn.style.backgroundColor = '#e5e7eb';
                });
                cancelBtn.addEventListener('mouseleave', () => {
                    cancelBtn.style.backgroundColor = '#f3f4f6';
                });

                deleteBtn.addEventListener('mouseenter', () => {
                    deleteBtn.style.backgroundColor = '#b91c1c';
                });
                deleteBtn.addEventListener('mouseleave', () => {
                    deleteBtn.style.backgroundColor = '#dc2626';
                });

                // Handle button clicks
                const cleanup = () => {
                    modal.style.transform = 'scale(0.9)';
                    overlay.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 200);
                };

                cancelBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(false);
                });

                deleteBtn.addEventListener('click', () => {
                    cleanup();
                    resolve(true);
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        cleanup();
                        resolve(false);
                    }
                });

                // Close on Escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        cleanup();
                        resolve(false);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }
    </script>
{% endblock %}

{% block scripts %}
{% endblock %}
