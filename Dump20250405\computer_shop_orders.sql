-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: localhost    Database: computer_shop
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int DEFAULT NULL,
  `order_date` datetime DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('PENDING','COMPLETED','CANCELLED') DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES (1,1,'2025-04-01 14:00:00',299.99,'COMPLETED'),(2,2,'2025-04-02 10:30:00',499.99,'PENDING'),(3,3,'2025-04-03 16:45:00',129.99,'CANCELLED'),(4,4,'2025-04-04 12:15:00',59.99,'COMPLETED'),(5,5,'2025-04-05 09:00:00',89.99,'COMPLETED'),(6,6,'2025-04-06 15:40:00',89.99,'PENDING'),(7,7,'2025-04-07 10:20:00',249.99,'COMPLETED'),(8,8,'2025-04-08 18:10:00',159.99,'CANCELLED'),(9,9,'2025-04-09 09:55:00',479.99,'PENDING'),(10,10,'2025-04-10 14:45:00',229.99,'COMPLETED'),(11,11,'2025-04-11 12:35:00',119.99,'COMPLETED'),(12,12,'2025-04-12 17:00:00',299.99,'PENDING'),(13,13,'2025-04-13 10:45:00',399.99,'COMPLETED'),(14,14,'2025-04-14 13:30:00',189.99,'COMPLETED'),(15,15,'2025-04-15 16:20:00',279.99,'PENDING'),(16,16,'2025-04-16 11:10:00',99.99,'CANCELLED'),(17,17,'2025-04-17 19:00:00',359.99,'COMPLETED'),(18,18,'2025-04-18 08:40:00',459.99,'PENDING'),(19,19,'2025-04-19 14:25:00',159.99,'COMPLETED'),(20,20,'2025-04-20 12:50:00',109.99,'CANCELLED'),(21,21,'2025-04-21 10:10:00',299.99,'COMPLETED'),(22,22,'2025-04-22 15:30:00',199.99,'PENDING'),(23,23,'2025-04-23 11:00:00',349.99,'CANCELLED'),(24,24,'2025-04-24 13:15:00',469.99,'COMPLETED'),(25,25,'2025-04-25 14:40:00',149.99,'COMPLETED'),(26,26,'2025-04-26 18:30:00',249.99,'PENDING'),(27,27,'2025-04-27 09:20:00',369.99,'COMPLETED'),(28,28,'2025-04-28 15:45:00',459.99,'CANCELLED'),(29,29,'2025-04-29 12:25:00',159.99,'COMPLETED'),(30,30,'2025-04-30 10:35:00',199.99,'PENDING'),(31,31,'2025-05-01 14:50:00',279.99,'COMPLETED'),(32,32,'2025-05-02 13:10:00',89.99,'COMPLETED'),(33,33,'2025-05-03 15:30:00',479.99,'PENDING'),(34,34,'2025-05-04 09:40:00',139.99,'COMPLETED'),(35,35,'2025-05-05 14:10:00',229.99,'CANCELLED'),(36,36,'2025-05-06 16:15:00',319.99,'COMPLETED'),(37,37,'2025-05-07 10:20:00',199.99,'COMPLETED'),(38,38,'2025-05-08 12:45:00',349.99,'COMPLETED'),(39,39,'2025-05-09 17:30:00',89.99,'CANCELLED'),(40,1,'2025-04-01 14:00:00',299.99,'COMPLETED'),(41,2,'2025-04-02 10:30:00',499.99,'PENDING'),(42,3,'2025-04-03 16:45:00',129.99,'CANCELLED'),(43,4,'2025-04-04 12:15:00',59.99,'COMPLETED'),(44,5,'2025-04-05 09:00:00',89.99,'COMPLETED'),(45,6,'2025-04-06 15:40:00',89.99,'PENDING'),(46,7,'2025-04-07 10:20:00',249.99,'COMPLETED'),(47,8,'2025-04-08 18:10:00',159.99,'CANCELLED'),(48,9,'2025-04-09 09:55:00',479.99,'PENDING'),(49,10,'2025-04-10 14:45:00',229.99,'COMPLETED'),(50,11,'2025-04-11 12:35:00',119.99,'COMPLETED'),(51,12,'2025-04-12 17:00:00',299.99,'PENDING'),(52,13,'2025-04-13 10:45:00',399.99,'COMPLETED'),(53,14,'2025-04-14 13:30:00',189.99,'COMPLETED'),(54,15,'2025-04-15 16:20:00',279.99,'PENDING'),(55,16,'2025-04-16 11:10:00',99.99,'CANCELLED'),(56,17,'2025-04-17 19:00:00',359.99,'COMPLETED'),(57,18,'2025-04-18 08:40:00',459.99,'PENDING'),(58,19,'2025-04-19 14:25:00',159.99,'COMPLETED'),(59,20,'2025-04-20 12:50:00',109.99,'CANCELLED'),(60,21,'2025-04-21 10:10:00',299.99,'COMPLETED'),(61,22,'2025-04-22 15:30:00',199.99,'PENDING'),(62,23,'2025-04-23 11:00:00',349.99,'CANCELLED'),(63,24,'2025-04-24 13:15:00',469.99,'COMPLETED'),(64,25,'2025-04-25 14:40:00',149.99,'COMPLETED'),(65,26,'2025-04-26 18:30:00',249.99,'PENDING'),(66,27,'2025-04-27 09:20:00',369.99,'COMPLETED'),(67,28,'2025-04-28 15:45:00',459.99,'CANCELLED'),(68,29,'2025-04-29 12:25:00',159.99,'COMPLETED'),(69,30,'2025-04-30 10:35:00',199.99,'PENDING'),(70,31,'2025-05-01 14:50:00',279.99,'COMPLETED'),(71,32,'2025-05-02 13:10:00',89.99,'COMPLETED'),(72,33,'2025-05-03 15:30:00',479.99,'PENDING'),(73,34,'2025-05-04 09:40:00',139.99,'COMPLETED'),(74,35,'2025-05-05 14:10:00',229.99,'CANCELLED'),(75,36,'2025-05-06 16:15:00',319.99,'COMPLETED'),(76,37,'2025-05-07 10:20:00',199.99,'COMPLETED'),(77,38,'2025-05-08 12:45:00',349.99,'COMPLETED'),(78,39,'2025-05-09 17:30:00',89.99,'CANCELLED');
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-05  9:56:20
