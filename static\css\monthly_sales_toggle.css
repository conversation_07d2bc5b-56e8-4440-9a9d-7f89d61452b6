/* Modern Professional Styles for Monthly Sales Toggle Buttons */

.monthly-sales-toggle-container {
  position: relative;
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.monthly-sales-toggle-button {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 32px 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  flex: 1;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  min-height: 140px;
}

.monthly-sales-toggle-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.monthly-sales-toggle-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #cbd5e1;
}

.monthly-sales-toggle-button:hover::before {
  opacity: 1;
}

.monthly-sales-toggle-button.active {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-color: #6366f1;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
  color: white;
}

.monthly-sales-toggle-button.active .monthly-sales-toggle-label,
.monthly-sales-toggle-button.active .monthly-sales-toggle-amount,
.monthly-sales-toggle-button.active .monthly-sales-toggle-profit {
  color: white;
}

.monthly-sales-toggle-label {
  font-weight: 600;
  font-size: 16px;
  color: #475569;
  margin-bottom: 12px;
  letter-spacing: -0.025em;
  position: relative;
  z-index: 1;
}

.monthly-sales-toggle-amount {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: -0.05em;
  position: relative;
  z-index: 1;
}

.monthly-sales-toggle-profit {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  position: relative;
  z-index: 1;
}

.monthly-sales-toggle-profit.positive {
  color: #059669;
}

.monthly-sales-toggle-profit.negative {
  color: #dc2626;
}

/* Add subtle icon indicators */
.monthly-sales-toggle-button .monthly-sales-toggle-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 20px;
  color: #cbd5e1;
  z-index: 1;
}

.monthly-sales-toggle-button.active .monthly-sales-toggle-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Professional responsive design */
@media (max-width: 768px) {
  .monthly-sales-toggle-container {
    flex-direction: column;
    gap: 16px;
  }

  .monthly-sales-toggle-button {
    padding: 24px 20px;
    min-height: 120px;
  }

  .monthly-sales-toggle-label {
    font-size: 15px;
    margin-bottom: 10px;
  }

  .monthly-sales-toggle-amount {
    font-size: 28px;
    margin-bottom: 6px;
  }

  .monthly-sales-toggle-profit {
    font-size: 12px;
  }
}

/* Enhanced focus states for accessibility */
.monthly-sales-toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Subtle animation for amount changes */
.monthly-sales-toggle-amount {
  transition: all 0.3s ease;
}

/* Professional loading state */
.monthly-sales-toggle-button.loading {
  opacity: 0.7;
  pointer-events: none;
}

.monthly-sales-toggle-button.loading .monthly-sales-toggle-amount::after {
  content: '...';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { opacity: 0; }
  50% { opacity: 1; }
  80%, 100% { opacity: 0; }
}
