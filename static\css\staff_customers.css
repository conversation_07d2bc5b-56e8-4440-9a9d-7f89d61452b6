
/* General container styling */
.staff-container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Heading */
.staff-container h1 {
    color: #333;
    margin-bottom: 30px;
    border-bottom: 3px solid #e67e22;
    padding-bottom: 10px;
    display: flex;
    justify-content: center;
}

/* Customer table styling */
.customer-table-container {
    overflow-x: auto;
}

.customer-table-container table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.customer-table-container th,
.customer-table-container td {
    padding: 14px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.customer-table-container th {
    background: #f3f4f6;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.customer-table-container td {
    font-size: 0.875rem;
    color: #1f2937;
}

.customer-table-container tr:hover {
    background: #f9fafb;
}

/* Action buttons styling - matching inventory page */
.action-buttons {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
}

.action-buttons .btn {
    width: 80px;
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 4px;
}

/* Modal button styling - matching inventory page */
.modal-footer .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
}

/* Mobile responsive button styling */
@media (max-width: 768px) {
    .action-buttons .btn {
        width: 80px;
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .customer-table-container table {
        display: none;
    }

    #mobile-customers-list {
        display: block !important;
    }

    .mobile-card {
        display: block;
    }
}

/* Pagination styling - minimal override to match other pages */
.pagination {
    margin-top: 20px;
    justify-content: center;
}

/* Search input styling */
#customerSearchInput {
    max-width: 400px;
    margin-bottom: 20px;
}

/* Add customer button styling - matching inventory page */
.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    margin-bottom: 20px;
    padding: 6px 12px;
    font-size: 0.9rem;
}

/* Mobile card styling */
#mobile-customers-list {
    display: none;
}

.mobile-card {
    display: none;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-card:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.mobile-card .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 10px;
}

.mobile-card .action-buttons .btn {
    width: 100%;
    max-width: 80px;
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .staff-container {
        padding: 10px;
    }

    .customer-table-container table {
        display: none;
    }

    .mobile-card {
        display: block;
    }

    #customerSearchInput {
        max-width: 100%;
        margin-bottom: 15px;
    }

    .btn-success {
        width: 100%;
        padding: 10px;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
}
