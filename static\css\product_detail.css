
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            background-color: white;
        }

        header, .top-bar, nav {
            width: 100%;
            text-align: center;
        }

        .top-bar {
            background-color: #333;
            color: white;
            padding: 10px 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
        }

        .top-bar .logo {
            height: 50px;
        }

        .top-bar span {
            font-size: 2em;
            margin-right: 20px;
        }

        nav {
            display: flex;
            justify-content: center;
            flex-grow: 1;
        }

        nav ul {
            
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        nav ul li {
            position: relative;
            margin: 0 15px;
            padding-top: 30px;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s;
            white-space: nowrap;
        }

        nav ul li a:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        /* Keyframes for slide-down animation (still here for dropdowns) */
        @keyframes slideDown {
            0% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dropdown menu styling */
        nav ul li .dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #333;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            min-width: 200px;
            padding: 15px 0;
            animation: slideDown 0.3s ease-in-out;
            margin-top: 10px;
        }

        /* Dropdown items */
        nav ul li .dropdown li {
            margin: 0;
            padding: 0;
        }

        nav ul li .dropdown li a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        nav ul li .dropdown li a:hover {
            background-color: #444;
            color: #fff;
        }

        /* Show the dropdown when hovering over the parent list item */
        nav ul li:hover .dropdown {
            display: block;
        }

        /* Search Container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            margin-left: auto;
            padding-right: 15px;
        }

        .search-icon {
            cursor: pointer;
            transition: transform 0.3s, filter 0.3s;
        }

        .search-icon img:hover {
            transform: scale(1.1);
            filter: brightness(1.2);
        }

        .search-icon img:active {
            transform: scale(0.9);
            filter: brightness(0.8);
        }

        .search-input {
            width: 0;
            padding: 0;
            border: none;
            outline: none;
            background-color: white;
            color: black;
            font-size: 16px;
            transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
            position: absolute;
            left: 30px;
            opacity: 0;
            pointer-events: none;
            border-radius: 50px;
        }

        /* Hover effect to slide out the input field */
        .search-container:hover .search-input,
        .search-container.active .search-input {
            width: 200px;
            padding: 5px 10px;
            opacity: 1;
            pointer-events: auto;
            border-bottom: 1px solid white;
        }

        /* --- Product Images Section --- */
        .product-images {
            display: flex; /* Use Flexbox for layout */
            flex-wrap: wrap; /* Allow images to wrap to the next line */
            justify-content: center; /* Center images horizontally */
            gap: 20px; /* Space between images */
            margin: 20px auto; /* Center the container and add vertical margin */
            max-width: 900px; /* Limit overall width of image section */
            padding: 0 15px; /* Add some padding on sides */
        }

        .product-images img {
            width: 100%; /* Default to full width on smaller screens */
            max-width: 400px; /* Max width for each image */
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            object-fit: contain; /* Ensures the entire image is visible without cropping */
            background-color: #f5f5f5; /* Light background for images with transparency */
            padding: 10px; /* Padding inside the image box */
        }

        /* Product Information */
        .product-info {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: left;
        }

        .product-info h1 {
            font-size: 2.5em;
            margin: 0 0 10px;
        }

        .price {
            font-size: 1.8em;
            color: #e67e22;
            margin: 0 0 20px;
        }

        .description {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 0 0 20px;
        }

        .specifications {
            margin: 20px 0;
        }

        .specifications h2 {
            font-size: 1.5em;
            margin: 0 0 10px;
        }

        .specifications ul {
            list-style-type: none;
            padding: 0;
        }

        .specifications ul li {
            margin: 10px 0;
        }

        .customization {
            margin: 20px 0;
        }

        .customization h2 {
            font-size: 1.5em;
            margin: 0 0 10px;
        }

        .customization label {
            display: block;
            margin: 10px 0 5px;
        }

        .customization select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 1em;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23000000%22%20d%3D%22M287%2C197.6c-3.2%2C3.2-8.3%2C3.2-11.6%2C0L146.2%2C68.6L16.2%2C197.6c-3.2%2C3.2-8.3%2C3.2-11.6%2C0c-3.2-3.2-3.2-8.3%2C0-11.6l135.4-135.4c3.2-3.2%2C8.3-3.2%2C11.6%2C0l135.4%2C135.4C290.2%2C189.3%2C290.2%2C194.4%2C287%2C197.6z%22%2F%3E%3C%2Fsvg%3E');
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 12px;
        }

        .add-to-cart {
            background-color: #e67e22;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
            max-width: 300px;
            display: block;
            margin: 20px auto;
        }

        .add-to-cart:hover {
            background-color: #d35400;
        }

        
.footer1 {
    background-color: #24262b;
    padding: 70px 0;
    color: white;
    
}

.container1 {
    max-width: 75%;
    margin: auto;
    
}

.row1 {
    display: flex;
    flex-wrap: wrap;
    
}

.footer1-col {
    width: 20%;
    padding: 0 35px;
    
}

.footer1-col h4 {
    font-size: 18px;
    text-transform: capitalize;
    margin-bottom: 35px;
    font-weight: 500;
    position: relative;
    
}

.footer1-col h4::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    background-color: #e91e63;
    height: 2px;
    width: 50px;
    
}

.footer1-col ul li {
    margin-bottom: 10px;
    margin-left: -15px;
    padding-left: 10px;
    list-style: none;
    
    /* background-color: #0d6efd; */
}

.footer1-col ul li a {
    font-size: 16px;
    text-transform: capitalize;
    color: #bbbbbb;
    text-decoration: none;
    font-weight: 300;
    display: block;
    transition: all 0.3s ease;
    
    
}

.footer1-col ul li a:hover, .footer-col ul li a:focus {
    color: #ffffff;
    padding-left: 8px;
}

.footer1-col .social-links a {
    display: inline-block;
    height: 40px;
    width: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0 10px 10px 0;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    color: #ffffff;
    transition: all 0.5s ease;
}

.footer1-col .social-links a:hover, .footer-col .social-links a:focus {
    color: #24262b;
    background-color: #ffffff;
}


        /* --- Media Queries for Responsiveness --- */

        /* For screens smaller than 1024px (Laptops/Tablets) */
        @media (max-width: 1024px) {
            .top-bar span {
                font-size: 1.8em;
                margin-bottom: 10px;
            }

            nav ul {
                flex-direction: column;
                align-items: flex-start;
            }

            nav ul li {
                padding-top: 10px;
                margin: 5px 0;
            }

            nav ul li .dropdown {
                position: static;
                min-width: auto;
                box-shadow: none;
                border-radius: 0;
                padding: 0;
                margin-top: 0;
            }

            .search-container {
                width: 100%;
                justify-content: center;
                margin-top: 10px;
                padding-right: 0;
            }

            .search-input {
                left: auto;
                position: relative;
                width: 100%;
                max-width: 250px;
                padding: 5px 10px;
                opacity: 1;
                pointer-events: auto;
                border-bottom: 1px solid #ccc;
                margin-left: 10px;
            }

            .search-container:hover .search-input,
            .search-container.active .search-input {
                width: 100%;
                max-width: 250px;
            }

            .product-images img {
                max-width: 300px; /* Adjust max-width for images on tablets */
            }

            .product-info {
                padding: 15px;
            }

            .product-info h1 {
                font-size: 2em;
            }

            .price {
                font-size: 1.5em;
            }

            .description {
                font-size: 1em;
            }

            .specifications h2,
            .customization h2 {
                font-size: 1.3em;
            }
        }

        /* For screens smaller than 768px (Tablets/Phablets) */
        @media (max-width: 768px) {
            .top-bar {
                flex-direction: column;
            }

            .top-bar span {
                margin-bottom: 15px;
            }

            nav ul {
                width: 100%;
                justify-content: center;
            }

            nav ul li {
                width: 100%;
                text-align: center;
                margin: 5px 0;
            }

            nav ul li a {
                padding: 8px 10px;
            }

            nav ul li .dropdown {
                width: 100%;
                text-align: center;
            }

            .product-images {
flex-direction: column; /* Stack images vertically on smaller screens */
                align-items: center; /* Center stacked images */
                            }

            .product-images img {
                width: 90%; /* Take up more width on mobile */
                max-width: 350px; /* Ensure images don't get too wide on small screens */
            }

            .product-info h1 {
                font-size: 1.8em;
            }

            .price {
                font-size: 1.3em;
            }

            .description {
                font-size: 0.95em;
            }

            .add-to-cart {
                padding: 12px 20px;
                font-size: 1.1em;
            }
        }

        /* For screens smaller than 480px (Mobile Phones) */
        @media (max-width: 480px) {
            .top-bar span {
                font-size: 1.5em;
            }

            nav ul li a {
                font-size: 0.9em;
                padding: 6px 8px;
            }

            .search-input {
                max-width: 180px;
            }

            .product-images img {
                width: 95%; /* Even more width on very small screens */
            }

            .product-info h1 {
                font-size: 1.5em;
            }

            .price {
                font-size: 1.1em;
            }

            .description {
                font-size: 0.9em;
            }

            .specifications ul li {
                font-size: 0.9em;
            }

            .customization select {
                font-size: 0.9em;
                padding: 8px;
            }

            .add-to-cart {
                padding: 10px 15px;
                font-size: 1em;
            }
        }