.widget.todays-orders-widget {
    width: 100%;
    margin-top: 30px;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    max-height: 600px; /* Increased height to accommodate grand total */
    overflow: hidden; /* Prevent content from spilling out */
    display: flex;
    flex-direction: column;
}

.widget.todays-orders-widget h2 {
    font-size: 1.5em;
    margin-bottom: 10px;
    flex-shrink: 0; /* Prevent header from shrinking */
}

.widget.todays-orders-widget .widget-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.widget.todays-orders-widget .widget-content p {
    margin-bottom: 15px;
    color: #555;
    flex-shrink: 0; /* Prevent description from shrinking */
}

.widget.todays-orders-widget .chart-container {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden; /* Ensure container doesn't overflow */
    min-height: 200px; /* Minimum height for content */
}
