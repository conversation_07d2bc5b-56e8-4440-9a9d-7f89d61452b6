<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up Page</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Serif+Text:ital@0;1&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"> 
    <style>
        body, html {
            height: 100%;
            margin: 0;
            font-family: 'Courier New', Courier, monospace;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .signup-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: aliceblue;
        }

        .signup-container {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 20px 40px;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 20px;
            width: 70%;
        }

        .input-group {
            margin-bottom: 15px;
            text-align: left;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: black;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.2);
         
        }

        button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 15px;
            background-color: orange;
            color: white;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 10px;
        }

        button:hover {
            background-color: rgb(131, 87, 7);
        }

        .back-btn {
            background-color: #6c757d;
        }

        .back-btn:hover {
            background-color: #5a6268;
        }

        p {
            color: black;
        }

        a {
            color: orange;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        @media (min-width: 768px) {
            .signup-container {
                width: 17%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="signup-section">
            <div class="signup-container">
                <h2>Join Us Now</h2>
{% if error %}
    <div style="color: red; margin-bottom: 10px;">
        {{ error.split('\\n')[0] }}
    </div>
{% endif %}
<form action="/auth/register" method="POST">
                    <div class="input-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" required>
                    </div>
                    <div class="input-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" required>
                    </div>
                    <div class="input-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="input-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="input-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="input-group">
                        <label for="phone">Phone</label>
                        <input type="text" id="phone" name="phone">
                    </div>
                    <button type="submit">Sign Up</button>
                </form>
                <button class="back-btn" onclick="window.history.back()">Back</button>
                <p>Already have an account? <a href="/auth/login">Login</a></p>
            </div>
        </div>
    </div>
</body>
</html>
