<<<<<<<
    <div class="inventory-actions">
        <button class="btn btn-primary" id="add-product">Add New Product</button>
        <div class="inventory-search">
            <label for="search-products" class="sr-only">Search products</label>
            <input type="text" id="search-products" placeholder="Search products..." aria-label="Search products">
            <button class="btn btn-success" id="search-btn">Search</button>
        </div>
    </div>
=======
    <div class="inventory-actions">
        <button class="btn btn-primary" id="add-product">Add New Product</button>
        <div class="inventory-search">
            <label for="search-products" class="sr-only">Search products</label>
            <input type="text" id="search-products" placeholder="Search products..." aria-label="Search products">
            <button class="btn btn-success" id="search-btn">Search</button>
        </div>
    </div>

    <div class="bulk-actions" style="margin: 10px 0;">
        <button class="btn btn-warning" id="bulk-update-stock-btn" disabled>Bulk Update Stock</button>
        <button class="btn btn-danger" id="bulk-delete-btn" disabled>Bulk Delete</button>
    </div>
>>>>>>> REPLACE
```

```
<<<<<<<
            <thead>
                <tr>
                    <th data-sort="id" class="sortable">Product ID <span class="sort-indicator"></span></th>
                    <th data-sort="name" class="sortable">Name <span class="sort-indicator"></span></th>
                    <th>Description</th>
                    <th data-sort="price" class="sortable">Price <span class="sort-indicator"></span></th>
                    <th data-sort="stock" class="sortable">Stock <span class="sort-indicator"></span></th>
                    <th>Actions</th>
                </tr>
            </thead>
=======
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all"></th>
                    <th data-sort="id" class="sortable">Product ID <span class="sort-indicator"></span></th>
                    <th data-sort="name" class="sortable">Name <span class="sort-indicator"></span></th>
                    <th>Description</th>
                    <th data-sort="price" class="sortable">Price <span class="sort-indicator"></span></th>
                    <th data-sort="stock" class="sortable">Stock <span class="sort-indicator"></span></th>
                    <th>Actions</th>
                </tr>
            </thead>
>>>>>>> REPLACE
```

```
<<<<<<<
    <!-- Add Product Modal -->
    <div id="add-product-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Add New Product</h2>
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">Product Name</label>
                    <input type="text" id="product-name" required>
                </div>
                <div class="form-group">
                    <label for="product-desc">Description</label>
                    <textarea id="product-desc" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="product-price">Price</label>
                    <input type="number" id="product-price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-stock">Initial Stock</label>
                    <input type="number" id="product-stock" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-category">Category (optional)</label>
                    <input type="text" id="product-category">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn">Add Product</button>
                </div>
            </form>
        </div>
    </div>
=======
    <!-- Add Product Modal -->
    <div id="add-product-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Add New Product</h2>
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">Product Name</label>
                    <input type="text" id="product-name" required>
                </div>
                <div class="form-group">
                    <label for="product-desc">Description</label>
                    <textarea id="product-desc" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="product-price">Price</label>
                    <input type="number" id="product-price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-stock">Initial Stock</label>
                    <input type="number" id="product-stock" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-category">Category (optional)</label>
                    <input type="text" id="product-category">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn">Add Product</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Update Stock Modal -->
    <div id="bulk-update-stock-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Bulk Update Stock</h2>
            <form id="bulk-stock-form">
                <div class="form-group">
                    <label for="bulk-stock-value">New Stock Value</label>
                    <input type="number" id="bulk-stock-value" min="0" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Stock</button>
                </div>
            </form>
        </div>
    </div>
>>>>>>> REPLACE
```

```
<<<<<<<
document.addEventListener('DOMContentLoaded', function() {
    // Stock update handling
    document.querySelectorAll('.update-stock').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const stockInput = document.querySelector(`.stock-input[data-product-id="${productId}"]`);
            const newStock = stockInput.value;
            
            fetch(`/staff/inventory/${productId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ stock: newStock })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Stock updated successfully');
                } else {
                    alert('Failed to update stock');
                }
            });
        });
    });

    // Add product modal handling
    const addProductModal = document.getElementById('add-product-modal');
    const addProductBtn = document.getElementById('add-product');
    const closeModalBtn = document.querySelectorAll('.close-modal');
    
    addProductBtn.addEventListener('click', () => {
        addProductModal.style.display = 'block';
    });
    
    closeModalBtn.forEach(btn => {
        btn.addEventListener('click', () => {
            addProductModal.style.display = 'none';
            bulkUpdateStockModal.style.display = 'none';
        });
    });
    
    // Handle form submission
    document.getElementById('product-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('product-name').value,
            description: document.getElementById('product-desc').value,
            price: parseFloat(document.getElementById('product-price').value),
            stock: parseInt(document.getElementById('product-stock').value),
            category: document.getElementById('product-category').value || null
        };
        
        fetch('/staff/inventory/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Product created successfully!');
                window.location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    });
    
    // Product search handling
    document.getElementById('search-btn').addEventListener('click', function() {
        const query = document.getElementById('search-products').value.trim();
        if (query) {
            searchProducts(query);
        }
    });

    // Bulk action elements
    const bulkUpdateStockBtn = document.getElementById('bulk-update-stock-btn');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const selectAllCheckbox = document.getElementById('select-all');
    const bulkUpdateStockModal = document.getElementById('bulk-update-stock-modal');
    const bulkStockForm = document.getElementById('bulk-stock-form');

    // Enable/disable bulk action buttons based on selection
    function updateBulkActionButtons() {
        const anyChecked = document.querySelectorAll('.select-product:checked').length > 0;
        bulkUpdateStockBtn.disabled = !anyChecked;
        bulkDeleteBtn.disabled = !anyChecked;
    }

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.select-product');
        checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
        updateBulkActionButtons();
    });

    // Delegate event for dynamically added product checkboxes
    document.getElementById('inventory-table-body').addEventListener('change', function(e) {
        if (e.target.classList.contains('select-product')) {
            updateBulkActionButtons();
            // If any checkbox is unchecked, uncheck selectAllCheckbox
            if (!e.target.checked) {
                selectAllCheckbox.checked = false;
            } else {
                // If all checkboxes are checked, check selectAllCheckbox
                const allChecked = document.querySelectorAll('.select-product').length === document.querySelectorAll('.select-product:checked').length;
                selectAllCheckbox.checked = allChecked;
            }
        }
    });

    // Show bulk update stock modal
    bulkUpdateStockBtn.addEventListener('click', () => {
        bulkUpdateStockModal.style.display = 'block';
    });

    // Handle bulk stock update form submission
    bulkStockForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const newStock = parseInt(document.getElementById('bulk-stock-value').value);
        if (isNaN(newStock) || newStock < 0) {
            alert('Please enter a valid stock value.');
            return;
        }
        const selectedProductIds = Array.from(document.querySelectorAll('.select-product:checked')).map(cb => parseInt(cb.dataset.productId));
        if (selectedProductIds.length === 0) {
            alert('No products selected.');
            return;
        }

        fetch('/staff/inventory/bulk_update_stock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ product_ids: selectedProductIds, stock: newStock })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Stock updated successfully for selected products.');
                bulkUpdateStockModal.style.display = 'none';
                selectAllCheckbox.checked = false;
                updateBulkActionButtons();
                // Refresh inventory list
                window.location.reload();
            } else {
                alert('Failed to update stock: ' + data.error);
            }
        });
    });

    // Handle bulk delete button click
    bulkDeleteBtn.addEventListener('click', () => {
        const selectedProductIds = Array.from(document.querySelectorAll('.select-product:checked')).map(cb => parseInt(cb.dataset.productId));
        if (selectedProductIds.length === 0) {
            alert('No products selected.');
            return;
        }
        if (!confirm(`Are you sure you want to delete ${selectedProductIds.length} selected product(s)? This action cannot be undone.`)) {
            return;
        }

        fetch('/staff/inventory/bulk_delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ product_ids: selectedProductIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Selected products deleted successfully.');
                selectAllCheckbox.checked = false;
                updateBulkActionButtons();
                // Refresh inventory list
                window.location.reload();
            } else {
                alert('Failed to delete products: ' + data.error);
            }
        });
    });
});
