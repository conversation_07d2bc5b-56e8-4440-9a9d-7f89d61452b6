<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ product.name }} - Inventory Management</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/static/css/sweet-alert.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/staff_notifications.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            background-color: white;
        }

        header, .top-bar, nav {
            width: 100%;
            text-align: center;
        }

.top-bar {
    background-color: #333;
    color: white;
    padding: 10px 0;
    position: fixed;
    top: 0; /* Ensure it sticks to the top */
    left: 0; /* Align to the left edge */
    width: 100%; /* Full width */
    z-index: 1000; /* Ensure it stays above other content */
    
}

.top-bar span {
    font-size: 2em;
}

.top-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10px 0;
}

.logo-left {
    position: absolute;
    left: 20px;
    top: 90%;
    transform: translateY(-50%);
}

.logo {
    height: 110px;
    width: auto;
    border-radius: 8px;
  
}


.site-title {
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
    text-align: center;
}


nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

nav ul li {
    position: relative;
    margin: 0 15px;
    padding: 10px 0;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

nav ul li a:hover, nav ul li a:focus {
    background-color: #f0f0f0;
    color: #333;
    outline: none;
}

@keyframes slideDown {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

nav ul li .dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #333;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 200px;
    padding: 15px 0;
    animation: slideDown 0.3s ease-in-out;
}

nav ul li .dropdown li {
    margin: 0;
    padding: 0;
}

nav ul li .dropdown li a {
    display: block;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    transition: background-color 0.3s;
}

nav ul li .dropdown li a:hover, nav ul li .dropdown li a:focus {
    background-color: #444;
    color: #fff;
}

nav ul li:hover .dropdown, nav ul li:focus-within .dropdown {
    display: block;
}

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            margin-left: auto;
            padding-right: 15px;
        }

        .search-icon {
            cursor: pointer;
            transition: transform 0.3s, filter 0.3s;
        }

        .search-icon img:hover {
            transform: scale(1.1);
            filter: brightness(1.2);
        }

        .search-icon img:active {
            transform: scale(0.9);
            filter: brightness(0.8);
        }

        .search-input {
            width: 0;
            padding: 0;
            border: none;
            outline: none;
            background-color: white;
            color: black;
            font-size: 16px;
            transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
            position: absolute;
            left: 30px;
            opacity: 0;
            pointer-events: none;
            border-radius: 50px;
        }

        .search-container:hover .search-input,
        .search-container.active .search-input {
            width: 200px;
            padding: 5px 10px;
            opacity: 1;
            pointer-events: auto;
            border-bottom: 1px solid white;
        }

        .product-images {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin: 20px auto;
            max-width: 900px;
            padding: 0 15px;
        }

        .product-images img {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            object-fit: contain;
            background-color: #f5f5f5;
            padding: 10px;
        }

        .product-info {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: left;
        }

        .product-info h1 {
            font-size: 2.5em;
            margin: 0 0 10px;
        }

        .price {
            font-size: 1.8em;
            color: #e67e22;
            margin: 0 0 20px;
        }

        .description {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 0 0 20px;
        }

        .specifications {
            margin: 20px 0;
        }

        .specifications h2 {
            font-size: 1.5em;
            margin: 0 0 10px;
        }

        .specifications ul {
            list-style-type: none;
            padding: 0;
        }

        .specifications ul li {
            margin: 10px 0;
        }

        .customization {
            margin: 20px 0;
        }

        .customization h2 {
            font-size: 1.5em;
            margin: 0 0 10px;
        }

        .customization label {
            display: block;
            margin: 10px 0 5px;
        }

        .customization select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 1em;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23000000%22%20d%3D%22M287%2C197.6c-3.2%2C3.2-8.3%2C3.2-11.6%2C0L146.2%2C68.6L16.2%2C197.6c-3.2%2C3.2-8.3%2C3.2-11.6%2C0c-3.2-3.2-3.2-8.3%2C0-11.6l135.4-135.4c3.2-3.2%2C8.3-3.2%2C11.6%2C0l135.4%2C135.4C290.2%2C189.3%2C290.2%2C194.4%2C287%2C197.6z%22%2F%3E%3C%2Fsvg%3E');
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 12px;
        }

        .add-to-cart {
            background-color: #e67e22;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: Ascension Island;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
            max-width: 300px;
            display: block;
            margin: 20px auto;
            text-align: center;
            text-decoration: none;
        }

        .add-to-cart:hover {
            background-color: #d35400;
        }

        .preorder-btn {
            background-color: #ffc107;
            color: #000;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
            max-width: 300px;
            display: block;
            margin: 20px auto;
            text-align: center;
            text-decoration: none;
        }

        .preorder-btn:hover {
            background-color: #e0a800;
        }

        .footer1 {
            background-color: #24262b;
            padding: 70px 0;
            color: white;
        }

        .container1 {
            max-width: 75%;
            margin: auto;
        }

    .footer1 {
            background-color: #24262b;
            color: white;
            padding: 60px 0;
            margin-top: 40px;
            border-top: 4px solid #e67e22;
        }

        .container1 {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .row1 {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
        }

        .footer1-col {
            flex: 1;
            min-width: 200px;
            padding: 0 15px;
            margin-bottom: 30px;
        }

        .footer1-col h4 {
            font-size: 18px;
            text-transform: capitalize;
            margin-bottom: 25px;
            font-weight: 500;
            position: relative;
        }

        .footer1-col h4::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -10px;
            background-color: #e67e22;
            height: 2px;
            width: 50px;
        }

        .footer1-col ul {
            list-style: none;
            padding: 0;
        }

        .footer1-col ul li {
            margin-bottom: 12px;
        }

        .footer1-col ul li a {
            font-size: 16px;
            text-transform: capitalize;
            color: #bbbbbb;
            text-decoration: none;
            font-weight: 300;
            display: block;
            transition: all 0.3s ease;
        }

        .footer1-col ul li a:hover, .footer1-col ul li a:focus {
            color: #ffffff;
            padding-left: 8px;
        }

        .footer1-col .qr-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .footer1-col .qr-container img {
            width: 170px;
            height: 170px;
            border: 2px solid #e67e22;
            border-radius: 5px;
        }

        .footer1-col .address-text {
            font-size: 14px;
            color: #bbbbbb;
            line-height: 1.6;
        }


        /* Hero Slider Styles */
  .hero-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 100px auto 0; /* 100px top margin to avoid fixed header, auto for horizontal centering, 0 bottom */
    overflow: hidden;
}

        .hero-section {
            display: none;
            width: 100%;
            height: 50vh;
            text-align: center;
            padding: 20px;

        }

        .hero-section.active {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-image {
            max-width: 50%;
            height: auto;
            border-radius: 10px;
            margin-right: 20px;
        }

        .hero-details {
            max-width: 50%;
            text-align: left;
        }

        .hero-details h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .hero-details .subheading {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 10px;
        }

        .hero-details h2 {
            font-size: 1.8em;
            color: #e67e22;
        }

        .slider-controls {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            transform: translateY(-50%);
        }

        .slider-controls button {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            padding: 10px;
            cursor: pointer;
            font-size: 1.5em;
            border-radius: 5px;
        }

        .slider-controls button:hover {
            background-color: rgba(0, 0, 0, 0.8);
        }

        /* Media Queries for Responsiveness */
        @media (max-width: 1024px) {
            .top-bar span {
                font-size: 1.8em;
                margin-bottom: 10px;
            }

            nav ul {
                flex-direction: column;
                align-items: flex-start;
            }

            nav ul li {
                padding-top: 10px;
                margin: 5px 0;
            }

            nav ul li .dropdown {
                position: static;
                min-width: auto;
                box-shadow: none;
                border-radius: 0;
                padding: 0;
                margin-top: 0;
            }

            .search-container {
                width: 100%;
                justify-content: center;
                margin-top: 10px;
                padding-right: 0;
            }

            .search-input {
                left: auto;
                position: relative;
                width: 100%;
                max-width: 250px;
                padding: 5px 10px;
                opacity: 1;
                pointer-events: auto;
                border-bottom: 1px solid #ccc;
                margin-left: 10px;
            }

            .search-container:hover .search-input,
            .search-container.active .search-input {
                width: 100%;
                max-width: 250px;
            }

            .product-images img {
                max-width: 300px;
            }

            .product-info {
                padding: 15px;
            }

            .product-info h1 {
                font-size: 2em;
            }

            .price {
                font-size: 1.5em;
            }

            .description {
                font-size: 1em;
            }

            .specifications h2,
            .customization h2 {
                font-size: 1.3em;
            }

            .hero-section.active {
                flex-direction: column;
            }

            .hero-image {
                max-width: 100%;
                margin-right: 0;
                margin-bottom: 20px;
            }

            .hero-details {
                max-width: 100%;
                text-align: center;
            }

            .hero-details h1 {
                font-size: 2em;
            }

            .hero-details .subheading {
                font-size: 1em;
            }

            .hero-details h2 {
                font-size: 1.5em;
            }
        }

        @media (max-width: 768px) {
            .top-bar {
                flex-direction: column;
            }

            .top-bar span {
                margin-bottom: 15px;
            }

            nav ul {
                width: 100%;
                justify-content: center;
            }

            nav ul li {
                width: 100%;
                text-align: center;
                margin: 5px 0;
            }

            nav ul li a {
                padding: 8px 10px;
            }

            nav ul li .dropdown {
                width: 100%;
                text-align: center;
            }

            .product-images {
                flex-direction: column;
                align-items: center;
            }

            .product-images img {
                width: 90%;
                max-width: 350px;
            }

            .product-info h1 {
                font-size: 1.8em;
            }

            .price {
                font-size: 1.3em;
            }

            .description {
                font-size: 0.95em;
            }

            .add-to-cart {
                padding: 12px 20px;
                font-size: 1.1em;
            }

            .preorder-btn {
                padding: 12px 20px;
                font-size: 1.1em;
            }
        }

        @media (max-width: 480px) {
            .top-bar span {
                font-size: 1.5em;
            }

            nav ul li a {
                font-size: 0.9em;
                padding: 6px 8px;
            }

            .search-input {
                max-width: 180px;
            }

            .product-images img {
                width: 95%;
            }

            .product-info h1 {
                font-size: 1.5em;
            }

            .price {
                font-size: 1.1em;
            }

            .description {
                font-size: 0.9em;
            }

            .specifications ul li {
                font-size: 0.9em;
            }

            .customization select {
                font-size: 0.9em;
                padding: 8px;
            }

            .add-to-cart {
                padding: 10px 15px;
                font-size: 1em;
            }

            .preorder-btn {
                padding: 10px 15px;
                font-size: 1em;
            }
        }

        /* Username text styling */
        .username-text {
            font-size: 0.95rem !important;
            font-weight: 400;
        }

        /* Notification styles */
        .notification-badge {
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 0px 2px;
            font-size: 0.4rem;
            position: relative;
            top: -2px;
            margin-left: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 12px;
            height: 12px;
        }

        .notification-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .notification-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80%;
            overflow-y: auto;
        }

        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }

        .notification-item.unread {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }

        .notification-item.order_cancelled {
            border-left: 4px solid #dc3545;
        }

        .notification-item.recent {
            border: 1px solid #28a745;
            background-color: #f8fff9;
        }

        .notification-date {
            color: #666;
            font-size: 12px;
        }

        .close-modal {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover,
        .close-modal:focus {
            color: black;
            text-decoration: none;
        }

.top-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10px 0;
}

.logo-left {
    position: absolute;
    left: 20px;
    top: 90%;
    transform: translateY(-50%);
}

.logo {
    height: 110px;
    width: auto;
    border-radius: 8px;
  
}


.site-title {
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-container {
    height: 400px;
    margin-top: 130px; /* To avoid overlap with fixed top-bar */
  }
  .hero-image {
    max-width: 50%;
    margin: 0 auto;
  }
  .category {
    width: 120px;
    flex: 1 1 auto; /* allow flex wrap */
  }
  nav ul {
    flex-direction: column;
    align-items: center; /* center nav items on smaller screens */
    padding-left: 0;
  }
  nav ul li {
    margin: 8px 0;
    width: 100%;
    text-align: left;
  }
  nav ul li a {
    display: block;
    width: 100%;
  }
  .search-container.active .search-input {
    width: 90%;
    left: 5%;
    position: relative; /* to fit inside container */
  }
  .footer1-col {
    width: 50%;
    margin-bottom: 30px;
    text-align: center;
  }
  .logo-left {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
  .logo {
    height: 80px;
  }
}

@media (max-width: 576px) {
  .hero-container {
    height: 300px;
    margin-top: 130px;
  }
  .hero-section h1 {
    font-size: 1em;
  }
  .hero-section .subheading {
    font-size: 1em;
  }
  .hero-section h2 {
    font-size: 1.2em;
  }
  .category {
    width: 100px;
    flex: 1 1 auto;
  }
  .footer1-col {
    width: 100%;
    text-align: center;
  }
  .search-container.active .search-input {
    width: 95%;
    left: 2.5%;
    position: relative;
  }
  .logo {
    height: 60px;
  }
  nav ul li {
    margin: 6px 0;
  }
}

/* Hamburger button hidden on desktop, visible on mobile */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  margin-left: auto;
  padding: 5px 10px;
  user-select: none;
}

@media (max-width: 768px) {
  .nav-toggle {
    display: block;
  }

  /* Hide nav menu by default on mobile */
  nav {
    display: none;
    width: 100%;
    background-color: #333;
  }

  nav.nav-open {
    display: block;
  }

  nav ul {
    flex-direction: column;
    align-items: flex-start;
    padding-left: 0;
    margin: 0;
  }

  nav ul li {
    width: 100%;
    margin: 10px 0;
  }

  nav ul li a {
    display: block;
    width: 100%;
  }

  /* Dropdown menus become static and full width */
  nav ul li .dropdown {
    position: static;
    box-shadow: none;
    border-radius: 0;
    min-width: 100%;
    padding-left: 15px;
  }

  /* Adjust top-header */
  .top-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    position: relative;
  }

 .logo-left {
    position: relative;
    margin: 0 auto;
    left: 0;
    transform: none;
  }
  .logo {
    height: 60px;
  }
}

/* Search input toggle */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  color: black;
}

.search-input {
  width: 0;
  padding: 0;
  border: none;
  outline: none;
  background-color: white;
  color: black;
  font-size: 16px;
  transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
  position: absolute;
  left: 30px;
  opacity: 0;
  pointer-events: none;
  border-radius: 50px;
}

.search-container.active .search-input {
  width: 200px;
  padding: 5px 10px;
  opacity: 1;
  pointer-events: auto;
  border: 1px solid #e67e22;
}

@media (max-width: 576px) {
  .search-container.active .search-input {
    width: 90vw;
    left: 5vw;
    position: fixed;
    top: 60px;
    z-index: 1500;
    
  }
}


/* Updated Responsive Footer */
@media (max-width: 992px) {
  .row1 {
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  .footer1-col {
    width: 80%;
    text-align: center;
  }

  .footer1-col .qr-container {
    flex-direction: column;
    justify-content: center;
  }

  .footer1-col .qr-container img {
    width: 150px;
    height: 150px;
  }

  .footer1-col .address-text {
    text-align: center;
  }
}

@media (max-width: 576px) {
  .footer1 {
    padding: 40px 0;
  }

  .footer1-col {
    width: 100%;
    padding: 0 10px;
  }

  .footer1-col h4::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer1-col ul li a {
    font-size: 14px;
  }

  .footer1-col .qr-container img {
    width: 130px;
    height: 130px;
  }

  .footer1-col .address-text {
    font-size: 13px;
  }
}

    </style>
</head>
<body>
    <header>
  <div class="top-bar">
    <div class="top-header">
      <div class="logo-left">
        <img src="/static/icons/logo.jpg" alt="Company Logo" class="logo" />
      </div>
      <div class="site-title">Russeykeo Computer</div>
      <!-- Hamburger button visible only on mobile -->
      <button
        class="nav-toggle"
        aria-label="Toggle navigation menu"
        aria-expanded="false"
      >
        &#9776;
      </button>
    </div>
    <nav>
      <ul>
        <li><a href="{{ url_for('show_dashboard') }}">Home</a></li>

        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Categories</a>
          <ul class="dropdown">
            <li><a href="/products/category/multi/1,5">Laptops</a></li>
            <li><a href="/products/category/2">Desktops</a></li>
            <li><a href="/products/category/3">Accessories</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">Products</a>
          <ul class="dropdown">
            <li><a href="/products/brand/dell">Dell</a></li>
            <li><a href="/products/brand/hp">HP</a></li>
            <li><a href="/products/brand/lenovo">Lenovo</a></li>
            <li><a href="/products/brand/asus">Asus</a></li>
            <li><a href="/products/brand/acer">Acer</a></li>
            <li><a href="/products/brand/razer">Razer</a></li>
          </ul>
        </li>
        <li>
          <a href="#" aria-haspopup="true" aria-expanded="false">About Company</a>
          <ul class="dropdown">
            <li><a href="{{ url_for('about') }}">About Us</a></li>
            <!-- <li><a href="{{ url_for('services') }}">Our Services</a></li>
            <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li> -->
          </ul>
        </li>
        <li><a href="{{ url_for('cart') }}">My Cart</a></li>
        {% if not session.username %}
        <li><a href="{{ url_for('auth.register') }}">Create Account</a></li>
        {% endif %}
        {% if session.username %}
          {% if session.role == 'customer' %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% elif session.role in ['staff', 'admin', 'super_admin'] %}
          <li><a href="{{ url_for('customer_preorders') }}">My Pre-Orders</a></li>
          <li>
            <a href="#" onclick="showNotifications()" id="notifications-link">
              Notifications
              <span
                id="notification-badge"
                class="notification-badge"
                style="display: none;"
                >0</span
              >
            </a>
          </li>
          {% endif %}
        {% endif %}
        {% if session.username %}
        <li class="nav-item dropdown">
          <a
            class="nav-link dropdown-toggle d-flex align-items-center text-white"
            href="#"
            id="userDropdown"
            role="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="bi bi-person-circle me-2"></i>
            <span class="username-text">{{ session.username }}</span>
          </a>
          <ul
            class="dropdown-menu dropdown-menu-end"
            aria-labelledby="userDropdown"
          >
            <li><hr class="dropdown-divider" /></li>
            <li>
              <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}"
                ><i class="bi bi-box-arrow-right me-2"></i>Logout</a
              >
            </li>
          </ul>
        </li>
        {% else %}
        <li class="nav-item">
          <a
            href="/auth/login"
            class="btn btn-outline-light btn-sm d-flex align-items-center"
          >
            <i class="bi bi-person-plus me-2"></i> Login Account
          </a>
        </li>
        {% endif %}
        <li>
          <form
            class="search-container"
            action="/search"
            method="GET"
            onsubmit="return validateSearch()"
          >
            <button
              type="button"
              class="search-icon"
              tabindex="0"
              aria-label="Search products"
              style="background:none; border:none; padding:0; cursor:pointer;"
            >
              <img
                src="/static/icons/search.png"
                alt="Search Icon"
                style="height: 20px"
              />
            </button>
            <input
              type="text"
              name="q"
              class="search-input"
              placeholder="Search..."
              aria-label="Search products"
              required
            />
            <div class="search-suggestions"></div>
          </form>
        </li>
      </ul>
    </nav>
  </div>
</header>

    <div class="content"> 
        <div class="hero-container">
            {% set product_images = [product.photo, product.back_view, product.left_rear_view, product.right_rear_view] %}
            {% for image in product_images %}
                {% if image %}
                <div class="hero-section {% if loop.first %}active{% endif %}">
                    <img src="{{ url_for('static', filename='uploads/products/' + image) }}" alt="{{ product.name }}" class="hero-image">
                </div>
                {% endif %}
            {% endfor %}
            <div class="slider-controls">
                <button onclick="prevSlide()">❮</button>
                <button onclick="nextSlide()">❯</button>
            </div>
        </div>
    </div>

    <main class="product-detail"
          data-product-id="{{ product.id }}"
          data-product-name="{{ product.name }}"
          data-product-price="{{ product.price }}"
          data-product-photo="{{ product.photo }}"
          data-expected-restock="{{ product.expected_restock_date.strftime('%B %d, %Y') if product.expected_restock_date else 'To be announced' }}">
        <div class="product-info">
            <h1>{{ product.name }}</h1>
            <p class="price">${{ "%.2f" | format(product.price) }}</p>
            <p class="description">{{ product.description }}</p>
            <div class="specifications">
                <h2>Specifications</h2>
                <ul>
                    {% if product.display %}<li><strong>Display:</strong> {{ product.display }}</li>{% endif %}
                    {% if product.ram %}<li><strong>RAM:</strong> {{ product.ram }}</li>{% endif %}
                    {% if product.storage %}<li><strong>Storage:</strong> {{ product.storage }}</li>{% endif %}
                    {% if product.battery %}<li><strong>Battery Life:</strong> {{ product.battery }}</li>{% endif %}
                    {% if product.os %}<li><strong>Operating System:</strong> {{ product.os }}</li>{% endif %}
                    {% if product.cpu %}<li><strong>CPU:</strong> {{ product.cpu }}</li>{% endif %}
                    {% if product.graphics %}<li><strong>Graphics:</strong> {{ product.graphics }}</li>{% endif %}
                    {% if product.weight %}<li><strong>Weight:</strong> {{ product.weight }}</li>{% endif %}
                    {% if product.color %}<li><strong>Color:</strong> {{ product.color }}</li>{% endif %}
                    {% if product.warranty_name %}<li><strong>Warranty:</strong> {{ product.warranty_name }}</li>{% endif %}
                    {% if product.keyboard %}<li><strong>Keyboard:</strong> {{ product.keyboard }}</li>{% endif %}
                </ul>
            </div>

            <!-- Stock Status Display -->
            <div class="stock-status mb-3">
                {% if product.stock_quantity > 0 %}
                    <span class="badge bg-success fs-6">
                        <i class="bi bi-check-circle"></i> In Stock
                    </span>
                {% else %}
                    <span class="badge bg-danger fs-6">
                        <i class="bi bi-x-circle"></i> Out of Stock
                    </span>
                    {% if product.expected_restock_date %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> Expected back in stock: {{ product.expected_restock_date.strftime('%B %d, %Y') if product.expected_restock_date else 'TBA' }}
                            </small>
                        </div>
                    {% endif %}
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                {% if session.username %}
                    {% if product.stock_quantity > 0 %}
                        <button type="button" class="add-to-cart" data-product-id="{{ product.id }}">Add to Cart</button>
                    {% elif product.allow_preorder %}
                        <button type="button" class="preorder-btn" data-bs-toggle="modal" data-bs-target="#preorderModal" data-product-id="{{ product.id }}">
                            <i class="bi bi-clock"></i> Pre-Order Now
                        </button>
                    {% else %}
                        <button type="button" class="btn btn-secondary" disabled>
                            <i class="bi bi-x-circle"></i> Unavailable
                        </button>
                    {% endif %}
                {% else %}
                    {% if product.stock_quantity > 0 %}
                        <button type="button" class="add-to-cart" onclick="showLoginAlert()">Add to Cart</button>
                    {% elif product.allow_preorder %}
                        <button type="button" class="preorder-btn" onclick="showLoginAlert()">
                            <i class="bi bi-clock"></i> Pre-Order Now
                        </button>
                    {% else %}
                        <button type="button" class="btn btn-secondary" disabled>
                            <i class="bi bi-x-circle"></i> Unavailable
                        </button>
                    {% endif %}
                {% endif %}
                <a href="{{ url_for('show_dashboard') }}" class="add-to-cart">Back</a>
            </div>
        </div>
    </main>

    <footer class="footer1">
        <div class="container1">
            <div class="row1">
                <div class="footer1-col">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="{{ url_for('about') }}">About Us</a></li>
                        <li><a href="{{ url_for('services') }}">Our Services</a></li>
                        <li><a href="{{ url_for('privacy') }}">Privacy Policy</a></li>
                       
                    </ul>
                </div>
                <div class="footer1-col">
                    <h4>Online Shop</h4>
                    <ul>
                        <li><a href="/products/category/multi/1,5">Laptops</a></li>
                        <li><a href="/products/category/2">Desktops</a></li>
                        <li><a href="/products/category/3">Accessories</a></li>
                    </ul>
                </div>
               <div class="footer1-col">
                <div class="footer1-col">
                    <h4>Shop Address</h4>
                    <div class="qr-container">
                            <img src="/static/icons/QR1.png" alt="Shop QR Code">
                    </div>
                       <div class="address-text">
                    <a href="https://maps.app.goo.gl/asmvfLTkajoffidN6?g_st=com.google.maps.preview.copy" 
                    target="_blank" 
                    style="text-decoration: none; color: #007BFF;" 
                    onmouseover="this.style.color='#0056b3'" 
                    onmouseout="this.style.color='#007BFF'">
                        No. 829B, entrance to Russey Keo High School, <br>
                        Sangkat Russey Keo, Khan Russey Keo, <br>
                        Phnom Penh.
                    </a>
                </div>
                </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Slider functionality
        const slides = document.querySelectorAll('.hero-section');
        let currentSlide = 0;

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        showSlide(currentSlide);

        // Function to show login alert
        function showLoginAlert() {
            Swal.question(
                'Login Required',
                'You must log in to add items to your cart. Please sign in to continue.',
                {
                    confirmButtonText: 'Login Now',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: 'success'
                }
            ).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '/auth/login';
                }
            });
        }

        // Function to add item to cart with toggle functionality
        async function addToCart(productId, buttonElement) {
            try {
                // Check if user is logged in first
                const userResponse = await fetch('/api/user/info');
                const userData = await userResponse.json();

                if (!userData.success || !userData.user) {
                    // User not logged in, redirect to login
                    showMessage('Please log in to add items to your cart.', 'warning');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 2000);
                    return;
                }

                // Disable button to prevent double clicks
                buttonElement.disabled = true;
                const originalContent = buttonElement.innerHTML;
                buttonElement.innerHTML = '<span>Adding...</span>';

                const response = await fetch('/api/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        quantity: 1
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update button to "Added" state
                    showAddedState(buttonElement);

                    // Show standardized success notification
                    showMessage('Item added to cart successfully!', 'success');
                } else {
                    // Restore original button state on error
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = originalContent;

                    // Show standardized error notification
                    showMessage('Error adding item to cart: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error:', error);

                // Restore original button state on error
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalContent;

                showMessage('An error occurred while adding the item to cart.', 'error');
            }
        }

        // Function to remove item from cart - DISABLED
        // This function has been disabled to prevent toggle behavior on product detail page
        // Users can only add items to cart, not remove them from this page
        /*
        async function removeFromCart(productId, buttonElement) {
            // This functionality has been intentionally disabled
            // Users should remove items from the cart page instead
            console.log('Remove from cart functionality disabled on product detail page');
        }
        */

        // Function to show "Added" state
        function showAddedState(buttonElement) {
            buttonElement.style.backgroundColor = '#28a745'; // Green
            buttonElement.style.color = '#fff';
            buttonElement.innerHTML = '<i class="bi bi-check-circle"></i> Added';
            buttonElement.disabled = true; // Disable button to prevent removal
            buttonElement.title = 'Item has been added to your cart';

            // Remove any existing click event listeners by cloning the element
            const newButton = buttonElement.cloneNode(true);
            buttonElement.parentNode.replaceChild(newButton, buttonElement);

            // No click event listener added - button is disabled and non-functional

            return newButton;
        }

        // Function to show default "Add to Cart" state
        function showDefaultState(buttonElement) {
            buttonElement.style.backgroundColor = '#e67e22'; // Orange
            buttonElement.style.color = '#fff';
            buttonElement.innerHTML = 'Add to Cart';
            buttonElement.disabled = false;

            // Remove any existing click event listeners by cloning the element
            const newButton = buttonElement.cloneNode(true);
            buttonElement.parentNode.replaceChild(newButton, buttonElement);

            // Add click event listener for add functionality
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = parseInt(newButton.getAttribute('data-product-id'));
                addToCart(productId, newButton);
            });

            return newButton;
        }

        // Add event listener for Add to Cart button with toggle functionality
        document.addEventListener('DOMContentLoaded', async function() {
            const addToCartBtn = document.querySelector('.add-to-cart[data-product-id]');
            if (addToCartBtn) {
                const productId = parseInt(addToCartBtn.getAttribute('data-product-id'));

                // Check if this product is already in cart
                try {
                    console.log(`🔍 Checking cart status for product ${productId}`);
                    const response = await fetch('/api/cart/items?t=' + Date.now()); // Cache busting
                    const data = await response.json();

                    console.log('🛒 Cart API response:', data);

                    if (data.success && data.cart_items) {
                        // Check if this product is in cart (regular items have 'id' field)
                        const isInCart = data.cart_items.some(item =>
                            item.id === productId
                        );

                        console.log(`🔍 Product ${productId} in cart:`, isInCart);

                        if (isInCart) {
                            // Show "Added" state for items already in cart
                            console.log(`✅ Product ${productId} is in cart, showing Added state`);
                            showAddedState(addToCartBtn);
                        } else {
                            // Add click event listener for add functionality
                            console.log(`🛒 Product ${productId} not in cart, adding click listener`);
                            addToCartBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                addToCart(productId, addToCartBtn);
                            });
                        }
                    } else {
                        console.log('🛒 No cart items or API error, adding click listener');
                        // Add click event listener for add functionality
                        addToCartBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            addToCart(productId, addToCartBtn);
                        });
                    }
                } catch (error) {
                    console.error('❌ Error checking cart status:', error);
                    // Add click event listener for add functionality as fallback
                    addToCartBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        addToCart(productId, addToCartBtn);
                    });
                }
            }
        });

        // Pre-order Modal Functionality
        let currentProductData = null;

        // Show pre-order modal
        document.addEventListener('DOMContentLoaded', function() {
            const preorderModal = document.getElementById('preorderModal');
            if (preorderModal) {
                preorderModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const productId = button.getAttribute('data-product-id');

                    // Get current product data from the page
                    const productDetail = document.querySelector('.product-detail');
                    currentProductData = {
                        id: parseInt(productDetail.dataset.productId),
                        name: productDetail.dataset.productName,
                        price: parseFloat(productDetail.dataset.productPrice),
                        photo: productDetail.dataset.productPhoto,
                        expected_restock_date: productDetail.dataset.expectedRestock
                    };

                    // Populate modal with product data
                    document.getElementById('preorder-product-image').src = '/static/uploads/products/' + currentProductData.photo;
                    document.getElementById('preorder-product-name').textContent = currentProductData.name;
                    document.getElementById('preorder-product-price').textContent = currentProductData.price.toFixed(2);
                    document.getElementById('preorder-expected-date').textContent = currentProductData.expected_restock_date;

                    // No quantity input needed - default to 1

                    updateDepositAmounts();
                });
            }

            // Handle deposit option changes
            const depositOptions = document.querySelectorAll('input[name="depositOption"]');
            depositOptions.forEach(option => {
                option.addEventListener('change', function() {
                    updateDepositAmounts();
                });
            });

            // Quantity change handling is now done in the modal setup above

            // Handle pre-order confirmation
            const confirmButton = document.getElementById('confirm-preorder');
            if (confirmButton) {
                confirmButton.addEventListener('click', submitPreOrder);
            }
        });

        function updateDepositAmounts() {
            if (!currentProductData) return;

            const quantity = 1; // Fixed quantity of 1 for pre-orders
            const totalPrice = currentProductData.price * quantity;

            document.getElementById('deposit25-amount').textContent = (totalPrice * 0.25).toFixed(2);
            document.getElementById('deposit50-amount').textContent = (totalPrice * 0.50).toFixed(2);
            document.getElementById('deposit100-amount').textContent = totalPrice.toFixed(2);
        }



        // Function to add pre-order to cart and redirect
        async function addPreOrderToCartAndRedirect(preOrderId, productId, productName, quantity, price) {
            console.log(`🛒 Adding pre-order #${preOrderId} to cart...`);

            try {
                // Add to cart without showing loading dialog
                const requestData = {
                    preorder_id: preOrderId,
                    product_id: productId,
                    quantity: quantity,
                    price: price
                };

                console.log('🛒 Sending request to add preorder to cart:', requestData);

                const response = await fetch('/api/cart/add-preorder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('📡 Response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('📦 Cart addition result:', result);

                    if (result.success) {
                        // Success - show success message
                        showMessage('Item added to cart successfully!', 'success');
                        return;
                    }
                }

                // If we get here, something went wrong - still show success for pre-order creation
                console.warn('Cart addition failed, but pre-order was created');
                showMessage('Item added to cart successfully!', 'success');

            } catch (error) {
                console.error('❌ Error adding pre-order to cart:', error);

                // Show success message for pre-order creation even if cart addition fails
                showMessage('✅ Pre-order created successfully!', 'success');
            }
        }



        async function submitPreOrder() {
            try {
                const quantity = 1; // Fixed quantity of 1 for pre-orders
                const depositPercentage = parseInt(document.querySelector('input[name="depositOption"]:checked').value);
                const notes = document.getElementById('preorder-notes').value;

                // Calculate the actual deposit amount to be paid
                const totalPrice = currentProductData.price * quantity;
                const depositAmount = (totalPrice * depositPercentage) / 100;

                const response = await fetch('/api/preorders/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: currentProductData.id,
                        quantity: quantity,
                        deposit_percentage: depositPercentage,
                        payment_method: null, // Payment will be handled in cart
                        notes: notes
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('preorderModal'));
                    modal.hide();

                    // Pass the deposit amount (not full price) to cart for payment
                    const priceForCart = depositAmount / quantity; // Price per item for cart display
                    addPreOrderToCartAndRedirect(data.pre_order_id, currentProductData.id, currentProductData.name, quantity, priceForCart);
                } else {
                    showMessage('Error placing pre-order: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('An error occurred while placing the pre-order.', 'error');
            }
        }
    </script>

    <!-- Pre-order Modal -->
    <div class="modal fade" id="preorderModal" tabindex="-1" aria-labelledby="preorderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="preorderModalLabel">
                        <i class="bi bi-clock"></i> Place Pre-Order - Initial Deposit
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <img id="preorder-product-image" src="" alt="Product Image" class="img-fluid rounded">
                        </div>
                        <div class="col-md-8">
                            <h6 id="preorder-product-name"></h6>
                            <p class="text-muted mb-2">
                                <strong>Price:</strong> $<span id="preorder-product-price"></span>
                            </p>
                            <p class="text-muted mb-3">
                                <strong>Expected Availability:</strong> <span id="preorder-expected-date"></span>
                            </p>

                            <form id="preorderForm">


                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-credit-card"></i> Initial Deposit Options
                                    </label>
                                    <div class="alert alert-info mb-3">
                                        <small><i class="bi bi-info-circle"></i> Choose your initial deposit amount. Payment will be processed through the cart.</small>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="depositOption" id="deposit25" value="25" checked>
                                        <label class="form-check-label" for="deposit25">
                                            <strong>25% Initial Deposit</strong> - $<span id="deposit25-amount">0.00</span>
                                            <small class="text-muted d-block">Pay 25% now, remaining 75% when available</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="depositOption" id="deposit50" value="50">
                                        <label class="form-check-label" for="deposit50">
                                            <strong>50% Initial Deposit</strong> - $<span id="deposit50-amount">0.00</span>
                                            <small class="text-muted d-block">Pay 50% now, remaining 50% when available</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="depositOption" id="deposit100" value="100">
                                        <label class="form-check-label" for="deposit100">
                                            <strong>Full Payment</strong> - $<span id="deposit100-amount">0.00</span>
                                            <small class="text-muted d-block">Pay full amount now, no additional payment needed</small>
                                        </label>
                                    </div>
                                </div>



                                <div class="mb-3">
                                    <label for="preorder-notes" class="form-label">Special Requests (Optional)</label>
                                    <textarea class="form-control" id="preorder-notes" rows="3" placeholder="Any special requests or notes..."></textarea>
                                </div>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Pre-order Terms:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>You will be notified when the product becomes available</li>
                                        <li>Deposits are refundable if you cancel before the product arrives</li>
                                        <li>You have 7 days to complete your purchase once notified</li>
                                        <li>Prices may be subject to change at time of availability</li>
                                    </ul>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="confirm-preorder">
                        <i class="bi bi-clock"></i> Confirm Pre-Order
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript for dropdown functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/sweet-alert.js"></script>
    <script src="/static/js/staff_messages.js"></script>

    <!-- Notification Modal -->
    <div id="notificationModal" class="notification-modal">
        <div class="notification-content">
            <span class="close-modal" onclick="closeNotifications()">&times;</span>
            <h2>Notifications</h2>
            <div id="notificationsList">
                <p>Loading notifications...</p>
            </div>
            <div style="margin-top: 20px;">
                <button onclick="markAllAsRead()" class="btn btn-primary">Mark All as Read</button>
                <button onclick="clearAllNotifications()" class="btn btn-warning">Clear All</button>
                <button onclick="closeNotifications()" class="btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Notification system
        let notifications = [];

        // Load notifications when page loads (for logged-in customers)
    </script>

    {% if session.username and session.role == 'customer' %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            // Check for new notifications every 30 seconds
            setInterval(loadNotifications, 30000);
        });
    </script>
    {% endif %}

    <script>
        function loadNotifications() {
            return fetch('/api/customer/notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        notifications = data.notifications;
                        updateNotificationBadge();

                        // Show custom notification for new unread notifications (only once per session)
                        const unreadNotifications = notifications.filter(n => !n.is_read);
                        if (unreadNotifications.length > 0) {
                            const newNotifications = unreadNotifications.filter(n => {
                                return !sessionStorage.getItem(`notification_shown_${n.id}`);
                            });

                            if (newNotifications.length > 0) {
                                // Mark as shown in session storage
                                newNotifications.forEach(n => {
                                    sessionStorage.setItem(`notification_shown_${n.id}`, 'true');
                                });

                                // Show standardized notification popup
                                const latestNotification = newNotifications[0];
                                setTimeout(() => {
                                    showMessage('New notification: ' + latestNotification.message, 'info');
                                }, 1000);
                            }
                        }
                    }
                    return data; // Return the data for chaining
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    throw error; // Re-throw for proper error handling
                });
        }

        function updateNotificationBadge() {
            const badge = document.getElementById('notification-badge');
            const unreadCount = notifications.filter(n => !n.is_read).length;

            // Show badge only for unread notifications
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'inline-flex';
                badge.style.backgroundColor = '#dc3545'; // Red for unread
                // Set balanced size styles
                badge.style.fontSize = '0.75rem';
                badge.style.minWidth = '20px';
                badge.style.height = '20px';
                badge.style.padding = '2px 6px';
                badge.style.marginLeft = '5px';
            } else {
                // Hide badge completely when all notifications are read
                badge.style.display = 'none';
            }
        }

        function showNotifications() {
            const modal = document.getElementById('notificationModal');
            const list = document.getElementById('notificationsList');

            if (notifications.length === 0) {
                list.innerHTML = '<p>No notifications</p>';
            } else {
                let html = '';
                const now = new Date();
                const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                notifications.forEach(notification => {
                    const readClass = notification.is_read ? '' : 'unread';
                    const typeClass = notification.type || '';
                    const notificationDate = new Date(notification.created_date);
                    const isRecent = notificationDate > oneDayAgo;
                    const recentClass = isRecent ? 'recent' : '';

                    html += `
                        <div class="notification-item ${readClass} ${typeClass} ${recentClass}" data-id="${notification.id}">
                            <div>${notification.message} ${isRecent && notification.is_read ? '<span style="color: #28a745; font-size: 12px;">(Recent)</span>' : ''}</div>
                            <div class="notification-date">${notificationDate.toLocaleString()}</div>
                            ${!notification.is_read ? '<button onclick="markAsRead(' + notification.id + ')" class="btn btn-sm btn-outline-primary" style="margin-top: 5px;">Mark as Read</button>' : ''}
                        </div>
                    `;
                });
                list.innerHTML = html;
            }

            modal.style.display = 'block';
        }

        function closeNotifications() {
            document.getElementById('notificationModal').style.display = 'none';
        }

        function markAsRead(notificationId) {
            // Disable the button immediately to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking...';

            fetch(`/api/customer/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh the notifications data and modal once
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark as Read';
            });
        }

        function markAllAsRead() {
            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Marking All...';

            fetch('/api/customer/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Only refresh once with proper chaining
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
                // Re-enable button on error
                button.disabled = false;
                button.textContent = 'Mark All as Read';
            });
        }

        function clearAllNotifications() {
            // Show warning message instead of confirm dialog
            showMessage('Are you sure you want to clear all notifications? Click the button again within 5 seconds to confirm.', 'warning');

            // Set a flag to track confirmation
            if (!window.clearNotificationsConfirmed) {
                window.clearNotificationsConfirmed = true;
                setTimeout(() => {
                    window.clearNotificationsConfirmed = false;
                }, 5000);
                return;
            }

            // Reset confirmation flag
            window.clearNotificationsConfirmed = false;

            // Disable the button to prevent double clicks
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Clearing All...';

            fetch('/api/customer/notifications/clear-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh notifications and update badge
                    loadNotifications().then(() => {
                        showNotifications(); // Refresh modal display after data is loaded
                    });
                } else {
                    showMessage('Error clearing notifications: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error clearing all notifications:', error);
                showMessage('An error occurred while clearing notifications.', 'error');
            })
            .finally(() => {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Clear All';
            });
        }

        // showCustomNotificationPopup function removed - now using standardized notification system

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('notificationModal');
            if (event.target == modal) {
                closeNotifications();
            }
        }


        document.addEventListener('DOMContentLoaded', () => {
  const navToggle = document.querySelector('.nav-toggle');
  const nav = document.querySelector('nav');
  const searchContainer = document.querySelector('.search-container');
  const searchIcon = document.querySelector('.search-icon');
  const searchInput = document.querySelector('.search-input');

  // Toggle nav menu on hamburger click
  navToggle.addEventListener('click', () => {
    const expanded = nav.classList.toggle('nav-open');
    navToggle.setAttribute('aria-expanded', expanded);
  });

  // Toggle search input on search icon click
  searchIcon.addEventListener('click', () => {
    searchContainer.classList.toggle('active');
    if (searchContainer.classList.contains('active')) {
      searchInput.focus();
    } else {
      searchInput.value = '';
    }
  });

  // Close nav or search if clicked outside
  document.addEventListener('click', (e) => {
    if (
      !nav.contains(e.target) &&
      !navToggle.contains(e.target)
    ) {
      nav.classList.remove('nav-open');
      navToggle.setAttribute('aria-expanded', false);
    }
    if (
      !searchContainer.contains(e.target)
    ) {
      searchContainer.classList.remove('active');
      searchInput.value = '';
    }
  });
});
    </script>

    <!-- Message Container for Standardized Notifications -->
    <div id="message-container"></div>
</body>
</html>