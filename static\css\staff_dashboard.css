.staff-widgets {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
    padding: 15px;
}

.widget {
    flex: 1 1 calc(50% - 20px);
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 25px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 300px;
    transition: all 0.3s ease;
}

.full-width-widget {
    flex: 1 1 100%;
}

.todays-orders-widget {
    flex: 1 1 100%;
    max-height: 600px; /* Increased height to show grand total */
    overflow: hidden; /* Ensure content stays within bounds */
}

.chart-container {
    position: relative;
    width: 100%;
    height: 300px;
}

.small-chart {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.centered-message {
    text-align: center;
    margin-top: 20px;
    display: none;
}

.widget h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.widget-content {
    padding: 15px 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.widget-content p {
    font-size: 16px;
    margin: 10px 0;
}

.widget-content .btn {
    align-self: flex-start;
    margin-top: auto;
}

.btn {
    display: inline-block;
    background: #4aaa57;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s;
    margin-top: 10px;
}

.btn:hover {
    background: #5ad607;
}

.widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .widget {
        flex: 1 1 100%;
        min-width: auto;
    }
}

.notifications-container ul li.low-stock {
    color: red;
}

.notifications-container ul li.in-stock {
    color: green;
}

/* Modal styles for filtered orders */
.modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
}

.notifications-container {
    height: 100%;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
    max-height: 300px; /* Added to control height */
}

.product-name-count-list {
    list-style: none;
    padding: 0;
    margin: 0;
    max-width: 320px;
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #764ba2 #e0e0e0;
}

.product-name-count-list::-webkit-scrollbar {
    width: 8px;
}

.product-name-count-list::-webkit-scrollbar-track {
    background: #e0e0e0;
    border-radius: 10px;
}

.product-name-count-list::-webkit-scrollbar-thumb {
    background-color: #764ba2;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
}

.product-name-count-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    padding: 14px 24px;
    margin-bottom: 14px;
    font-size: 18px;
    font-weight: 700;
    color: white;
    box-shadow: 0 6px 12px rgba(118, 75, 162, 0.5);
    transition: background 0.3s ease;
    cursor: default;
}

.product-name-count-item:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
}

.product-count-badge {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 16px;
    font-weight: 700;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    transition: background-color 0.3s ease;
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto; /* 10% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
    max-height: 70vh;
    overflow-y: auto;
    border-radius: 8px;
}

.close-button {
    color: #333 !important;
    float: right;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    outline: none !important;
}

.close-button:hover,
.close-button:focus {
    color: #000 !important;
    text-decoration: none;
    background: transparent !important;
    background-color: transparent !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

.custom-sized-widget {
    flex-basis: 50%; /* Adjusted to be closer to the default widget width */
    min-width: 250px; /* Ensure it doesn't become too small */
}

.custom-sized-widget {
    flex-basis: 10%; /* Example: Make this widget narrower */
    min-width: 250px; /* Ensure it doesn't become too small */
}

/* New styles for Inventory widget product count by brand list */
#product-name-count-list {
    font-family: Arial, sans-serif;
    margin-top: 10px;
}

.brand-panel {
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 12px 16px;
    cursor: pointer;
    
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.brand-panel:nth-child(odd) {
    background-color: #e6f0ff; /* light blue */
    color: #1a237e; /* dark blue */
}

.brand-panel:nth-child(even) {
    background-color: #f3e5f5; /* light purple */
    color: #4a148c; /* dark purple */
}

.brand-panel:nth-child(3n) {
    background-color: #fff3e0; /* light orange */
    color: #e65100; /* dark orange */
}

.brand-name {
    font-weight: 700;
    font-size: 1.1em;
}

.product-count {
    font-weight: 600;
    font-size: 0.9em;
    opacity: 0.8;
}

.toggle-icon {
    font-size: 1.2em;
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.brand-panel-content {
    margin-top: 8px;
    font-size: 0.9em;
    color: #333;
    display: none;
}

.brand-panel.expanded .toggle-icon {
    transform: rotate(180deg);
}

.brand-panel.expanded .brand-panel-content {
    display: block;
}

#lastUpdated {
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
    text-align: right;
}

/* Walk-in Sales Widget */
.walk-in-sales-widget .widget-content {
    padding: 20px;
}

.walk-in-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
}

.walk-in-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.walk-in-btn {
    width: 100%;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    transition: all 0.2s ease;
    background: #3b82f6;
    color: white;
    border: none;
}

.walk-in-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    background: #2563eb;
    color: white;
    text-decoration: none;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.quick-actions .btn {
    padding: 8px 12px;
    font-size: 0.875rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-actions .btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

/* Enhanced responsive design for walk-in widget */
@media (max-width: 768px) {
    .walk-in-stats {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        font-size: 1.125rem;
    }
}
