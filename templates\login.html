<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Serif+Text:ital@0;1&display=block" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"> 
    
<style>
    body, html {
        height: 100%;
        margin: 0;
        font-family: 'Courier New', Courier, monospace;
    }

    .container {
        display: flex;
        height: 100vh;
    }

    .login-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: aliceblue;
    }

    .login-container {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 20px 40px;
        border-radius: 15px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-bottom: 20px;
        width: 70%;
    }

    .form-group {
        margin-bottom: 25px;
        text-align: left;
    }

    .form-group label {
        display: block;
        margin-bottom: 15px;
        color: black;
        font-weight: bold;
        
    }

    .form-group input {
        width: 100%;
        padding: 10px;
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    button {
        width: 100%;
        padding: 10px;
        border: none;
        border-radius: 15px;
        background-color: orange;
        color: white;
        font-size: 1em;
        cursor: pointer;
        transition: background-color 0.3s;
        margin-bottom: 15px;
    }

    button:hover {
        background-color: rgb(126, 82, 1);
    }

    .back-btn {
        background-color: #6c757d;
    }

    .back-btn:hover {
        background-color: #5a6268;
    }

    p {
        color: black;
    }

    a {
        color: orange;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }

    .flash-messages {
        margin-bottom: 15px;
        color: #d8000c;
        background-color: rgba(255, 182, 193, 0.2);
        padding: 10px;
        border-radius: 10px;
        border: 1px solid #d8000c;
    }

    .flash-messages p {
        margin: 5px 0;
    }

    @media (min-width: 768px) {
        .login-container {
            width: 17%;
        }
    }
</style>
</head>
<body>
    <div class="container">
        <div class="login-section">
            <div class="login-container">
                <h1>Computer Shop Login</h1>
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        <div class="flash-messages">
                            {% for message in messages %}
                                <p>{{ message }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit">Login</button>
                </form>
                <button class="back-btn" onclick="window.location.href='/'">Back</button>
                <p>Don't have an account? <a href="{{ url_for('register') }}">Sign up</a></p>
            </div>
        </div>
    </div>
</body>
</html>