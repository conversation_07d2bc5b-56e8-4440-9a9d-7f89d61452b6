<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple E-commerce Cart</title>
    <link rel="stylesheet" href="/static/css/cart.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .cart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .product {
            border-bottom: 1px solid #ddd;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .product:last-child {
            border-bottom: none;
        }
        .product button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .product button:hover {
            background: #218838;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .cart-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            margin: 0 -10px;
            padding: 20px 10px;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .cart-item-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;
        }

        /* Product Type Badges */
        .product-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: 8px;
            vertical-align: middle;
        }

        .badge-preorder {
            background: linear-gradient(135deg, #ff9500, #ff6b00);
            color: white;
            box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
        }

        .badge-regular {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .product-name-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 4px;
        }

        .product-name {
            font-weight: 500;
            color: #333;
            font-size: 16px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 8px 0;
        }

        .quantity-controls button {
            background: #007bff;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .quantity-controls button:hover:not(:disabled) {
            background: #0056b3;
        }

        .quantity-controls button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .quantity-controls span {
            font-weight: 500;
            min-width: 60px;
            text-align: center;
        }

        .quantity-display {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
            height: fit-content;
        }

        .delete-btn:hover {
            background: #c82333;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .quantity-controls button:hover {
            background: #0056b3;
        }
        .quantity-controls button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }
        .delete-btn:hover {
            background: #c82333;
        }
        h2 {
            margin-top: 0;
        }
        #cart-count {
            font-weight: bold;
        }
        #buy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px;
            width: 48%;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
            font-size: 14px;
        }
        #buy-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        #buy-button:hover:not(:disabled) {
            background: #0056b3;
        }
        #back-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px;
            width: 48%;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-size: 14px;
        }
        #back-button:hover {
            background: #5a6268;
        }
        #cart-total {
            font-weight: bold;
            margin-top: 10px;
            text-align: right;
        }
        .cart-buttons {
            display: flex;
            justify-content: space-between;
        }

        /* Modal Styles */
        .modal {
            display: none; 
            position: fixed; 
            z-index: 1; 
            left: 0;
            top: 0;
            width: 100%; 
            height: 100%; 
            overflow: auto; 
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto; 
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            position: relative;
        }
        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 10px;
            right: 20px;
        }
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        #customer-form label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        #customer-form input {
            width: calc(100% - 20px);
            padding: 10px;
            margin-top: 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        #customer-form button {
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            font-size: 16px;
        }
        #customer-form button:hover {
            background: #218838;
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="cart">
            <h2>Shopping Cart (<span id="cart-count">0</span> items)</h2>
            <div id="cart-items"></div>
            <div id="cart-total">Total: $0.00</div>
            <div class="cart-buttons">
                <a href="{{ url_for('show_dashboard') }}" id="back-button">Continue Shopping</a>
                <button type="button" id="buy-button" disabled>Proceed to Checkout</button>
            </div>
        </div>
    </div>

    <div id="customer-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2>Customer Details</h2>
            <form id="customer-form">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" required>

                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>

                <label for="phone">Phone:</label>
                <input type="tel" id="phone" name="phone" required>

                <label for="address">Address:</label>
                <input type="text" id="address" name="address" required>

                <button type="submit">Submit Order</button>
            </form>
        </div>
    </div>

    <!-- Include QRCode.js library for QR code generation -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Include KHQR Payment JavaScript -->
    <script src="{{ url_for('static', filename='js/khqr_payment.js') }}"></script>
    <script>
        // Initialize cart items array
        let cartItems = [];

        // Get modal elements
        const modal = document.getElementById('customer-modal');
        const customerForm = document.getElementById('customer-form');
        const closeButton = document.querySelector('.close-button');

        async function deleteFromCart(id, isPreOrder = false) {
            try {
                const endpoint = isPreOrder ? '/api/cart/remove-preorder' : '/api/cart/remove';
                const bodyKey = isPreOrder ? 'preorder_id' : 'product_id';

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        [bodyKey]: id
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Remove item from local cartItems array
                    if (isPreOrder) {
                        cartItems = cartItems.filter(item => item.preorder_id !== id);
                    } else {
                        cartItems = cartItems.filter(item => item.id !== id);
                    }
                    updateCart();
                } else {
                    alert('Error removing item from cart: ' + data.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while removing the item from cart.');
            }
        }

        async function updateCartQuantity(id, quantity, isPreOrder = false) {
            try {
                const endpoint = isPreOrder ? '/api/cart/update-preorder' : '/api/cart/update';
                const bodyKey = isPreOrder ? 'preorder_id' : 'product_id';

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        [bodyKey]: id,
                        quantity: quantity
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update local cartItems array
                    const item = isPreOrder
                        ? cartItems.find(item => item.preorder_id === id)
                        : cartItems.find(item => item.id === id);
                    if (item) {
                        item.quantity = quantity;
                        updateCart();
                    }
                } else {
                    alert('Error updating cart: ' + data.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating the cart.');
            }
        }

        function updateCart() {
            console.log('🔄 updateCart() called with cartItems:', cartItems);
            console.log('🔄 cartItems.length:', cartItems.length);

            const cartItemsDiv = document.getElementById('cart-items');
            const cartCount = document.getElementById('cart-count');
            const cartTotal = document.getElementById('cart-total');
            const buyButton = document.getElementById('buy-button');

            cartItemsDiv.innerHTML = '';

            // Render cart items with delete buttons and quantity controls
            let totalItems = 0;
            let totalPrice = 0;
            cartItems.forEach(item => {
                totalItems += item.quantity;
                totalPrice += item.price * item.quantity;

                const cartItemDiv = document.createElement('div');
                cartItemDiv.className = 'cart-item';

                // Check if this is a pre-order item
                const isPreOrder = item.type === 'preorder';
                const itemId = isPreOrder ? item.preorder_id : item.id;

                let itemDisplay, priceDisplay, subtotalDisplay;

                if (isPreOrder) {
                    // For pre-orders, show deposit information with enhanced badge
                    const preOrderBadge = '<span class="product-badge badge-preorder">📦 Pre-Order</span>';
                    itemDisplay = `
                        <div class="product-name-container">
                            <span class="product-name">${item.name}</span>
                            ${preOrderBadge}
                        </div>
                    `;
                    priceDisplay = `$${item.price.toFixed(2)} (Deposit)`;
                    subtotalDisplay = `Deposit Total: $${(item.price * item.quantity).toFixed(2)}`;
                } else {
                    // For regular items, show normal pricing with regular badge
                    const regularBadge = '<span class="product-badge badge-regular">🛒 Order</span>';
                    itemDisplay = `
                        <div class="product-name-container">
                            <span class="product-name">${item.name}</span>
                            ${regularBadge}
                        </div>
                    `;
                    priceDisplay = `$${item.price.toFixed(2)}`;
                    subtotalDisplay = `Subtotal: $${(item.price * item.quantity).toFixed(2)}`;
                }

                // Quantity controls for both pre-orders and regular items
                const quantityControlsHTML = `
                    <div class="quantity-controls">
                        <button onclick="changeQuantity(${itemId}, ${item.quantity - 1}, ${isPreOrder})" ${item.quantity <= 1 ? 'disabled' : ''}>-</button>
                        <span>Qty: ${item.quantity}</span>
                        <button onclick="changeQuantity(${itemId}, ${item.quantity + 1}, ${isPreOrder})">+</button>
                    </div>
                `;

                cartItemDiv.innerHTML = `
                    <div class="cart-item-info">
                        <div style="margin-bottom: 8px;">
                            ${itemDisplay}
                            <div style="margin-top: 4px; color: #666; font-size: 14px;">${priceDisplay}</div>
                        </div>
                        ${quantityControlsHTML}
                        <span style="font-weight: 500; color: #333;">${subtotalDisplay}</span>
                    </div>
                    <button onclick="deleteFromCart(${itemId}, ${isPreOrder})" class="delete-btn">Remove</button>
                `;
                cartItemsDiv.appendChild(cartItemDiv);
            });

            cartCount.textContent = totalItems;
            cartTotal.textContent = `Total: $${totalPrice.toFixed(2)}`;
            buyButton.disabled = totalItems === 0;
        }

        function changeQuantity(id, newQuantity, isPreOrder = false) {
            if (newQuantity <= 0) {
                deleteFromCart(id, isPreOrder);
            } else {
                updateCartQuantity(id, newQuantity, isPreOrder);
            }
        }

        // Function to clear cart via API
        async function clearCartAPI() {
            try {
                const response = await fetch('/api/cart/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                if (data.success) {
                    cartItems = [];
                    updateCart();
                } else {
                    console.error('Error clearing cart:', data.error);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }









        // Expose functions to global scope for inline onclick handlers
        window.deleteFromCart = deleteFromCart;
        window.changeQuantity = changeQuantity;
        window.updateCartQuantity = updateCartQuantity;
        window.clearCartAPI = clearCartAPI;

        // Load cart items from server
        async function loadCartItems() {
            try {
                console.log('🛒 Loading cart items from server...');
                // Add cache-busting parameter to prevent browser caching
                const response = await fetch('/api/cart/items?t=' + Date.now());
                const data = await response.json();

                console.log('🛒 Cart API response:', data);
                console.log('🛒 Cart items received:', data.cart_items);
                console.log('🛒 Cart items length:', data.cart_items ? data.cart_items.length : 0);

                if (data.success) {
                    cartItems = data.cart_items || [];
                    console.log('🛒 Setting cartItems to:', cartItems);
                    updateCart();
                } else {
                    console.error('Error loading cart items:', data.error);
                    cartItems = [];
                    updateCart();
                }
            } catch (error) {
                console.error('Error:', error);
                cartItems = [];
                updateCart();
            }
        }

        // Initialize cart display when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadCartItems();
        });

        // Refresh cart when user returns to the page (e.g., after payment)
        window.addEventListener('focus', function() {
            console.log('🔄 Window focused - refreshing cart...');
            loadCartItems();
        });

        // Also refresh when page becomes visible (for mobile/tab switching)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('🔄 Page visible - refreshing cart...');
                loadCartItems();
            }
        });

        // --- Modal and Form Submission Logic ---

        // Open the modal when "Buy Now" is clicked - but skip if user is logged in
        document.getElementById('buy-button').onclick = async () => {
            if (cartItems.length > 0) {
                // Check if user is logged in and get their info
                try {
                    const userResponse = await fetch('/api/user/info');
                    const userData = await userResponse.json();

                    if (userData.success && userData.user) {
                        // User is logged in, proceed directly to payment
                        const customerInfo = {
                            first_name: userData.user.first_name,
                            last_name: userData.user.last_name,
                            email: userData.user.email,
                            phone: userData.user.phone || '',
                            address: userData.user.address || ''
                        };

                        // Calculate total price
                        let totalPrice = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);

                        // Show payment method selection
                        showPaymentMethodSelection(cartItems, customerInfo, totalPrice);
                    } else {
                        // User not logged in, show customer details modal
                        modal.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error checking user login status:', error);
                    // Fallback to showing modal
                    modal.style.display = 'block';
                }
            }
        };

        // Close the modal when the 'x' is clicked
        closeButton.onclick = () => {
            modal.style.display = 'none';
        };

        // Close the modal if the user clicks outside of the modal content
        window.onclick = (event) => {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };

        // Handle form submission - now using Bakong payment
        customerForm.addEventListener('submit', (event) => {
            event.preventDefault(); // Prevent page reload

            // Get customer data from the form
            const customerInfo = {
                first_name: document.getElementById('name').value.split(' ')[0] || '',
                last_name: document.getElementById('name').value.split(' ').slice(1).join(' ') || '',
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                address: document.getElementById('address').value
            };

            // Validate required fields
            if (!customerInfo.first_name || !customerInfo.email) {
                alert('Please fill in all required fields.');
                return;
            }

            // Calculate total price
            let totalPrice = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);

            // Close customer form modal
            modal.style.display = 'none';

            // Show payment method selection
            showPaymentMethodSelection(cartItems, customerInfo, totalPrice);
        });

        // Handle successful payment completion
        window.handlePaymentSuccess = async function(paymentData) {
            try {
                // Confirm payment and create order
                const response = await fetch(`/api/payment/confirm/${paymentData.session.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Cart is already cleared on server side after payment confirmation

                    // Reset customer form
                    customerForm.reset();

                    // Redirect to invoice page
                    window.location.href = `/invoice/${data.order_id}`;
                } else {
                    alert('Error creating order: ' + data.error);
                }
            } catch (error) {
                console.error('Error confirming payment:', error);
                alert('An error occurred while processing your order.');
            }
        };

        // Global variables to store payment data
        let currentPaymentData = {
            cartItems: [],
            customerInfo: {},
            totalPrice: 0
        };

        // Payment Method Selection Functions
        function showPaymentMethodSelection(cartItems, customerInfo, totalPrice) {
            // Store data globally instead of passing through HTML attributes
            currentPaymentData = {
                cartItems: cartItems,
                customerInfo: customerInfo,
                totalPrice: totalPrice
            };
            // Create payment method selection modal
            const paymentModal = document.createElement('div');
            paymentModal.id = 'payment-method-modal';
            paymentModal.className = 'modal';
            paymentModal.style.cssText = `
                display: block;
                position: fixed;
                z-index: 1001;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
            `;

            paymentModal.innerHTML = `
                <div class="modal-content" style="max-width: 600px; margin: 5% auto;">
                    <span class="close-button" onclick="closePaymentMethodModal()">&times;</span>
                    <h2 style="margin-bottom: 20px; color: #333;">Choose Payment Method</h2>
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="margin: 0 0 10px 0;">Order Summary</h3>
                        <p style="margin: 5px 0;"><strong>Customer:</strong> ${customerInfo.first_name} ${customerInfo.last_name}</p>
                        <p style="margin: 5px 0;"><strong>Total Amount:</strong> $${totalPrice.toFixed(2)}</p>
                        <p style="margin: 5px 0;"><strong>Items:</strong> ${cartItems.length} item(s)</p>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <!-- KHQR Payment Option -->
                        <div class="payment-option" style="border: 2px solid #ddd; border-radius: 10px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s;"
                             onclick="selectKHQRPayment()"
                             onmouseover="this.style.borderColor='#007bff'; this.style.backgroundColor='#f8f9ff'"
                             onmouseout="this.style.borderColor='#ddd'; this.style.backgroundColor='white'">
                            <div style="font-size: 48px; margin-bottom: 10px;">📱</div>
                            <h3 style="margin: 10px 0; color: #333;">KHQR Payment</h3>
                            <p style="margin: 0; color: #666; font-size: 14px;">ACLEDA Mobile, ABA, Wing, Pi Pay</p>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 12px;">Scan QR code with your banking app</p>
                        </div>

                        <!-- Cash Payment Option -->
                        <div class="payment-option" style="border: 2px solid #ddd; border-radius: 10px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s;"
                             onclick="selectCashPayment()"
                             onmouseover="this.style.borderColor='#28a745'; this.style.backgroundColor='#f8fff8'"
                             onmouseout="this.style.borderColor='#ddd'; this.style.backgroundColor='white'">
                            <div style="font-size: 48px; margin-bottom: 10px;">💵</div>
                            <h3 style="margin: 10px 0; color: #333;">Cash Payment</h3>
                            <p style="margin: 0; color: #666; font-size: 14px;">Walk-in customers</p>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 12px;">Pay with cash at the store</p>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closePaymentMethodModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            Cancel
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(paymentModal);
        }

        function closePaymentMethodModal() {
            const modal = document.getElementById('payment-method-modal');
            if (modal) {
                modal.remove();
            }
        }

        function selectKHQRPayment() {
            // Use global payment data instead of parsing from parameters
            const { cartItems, customerInfo, totalPrice } = currentPaymentData;

            closePaymentMethodModal();

            // Start KHQR payment process
            console.log('🔥 Starting KHQR payment:', { cartItems, customerInfo, totalPrice });

            // Set success callback for when payment is completed
            window.khqrPayment.setSuccessCallback(async (result) => {
                console.log('🎉 Cart success callback called with result:', result);
                console.log('🔍 Result details:', {
                    invoice_url: result.invoice_url,
                    order_id: result.order_id,
                    amount: result.amount,
                    currency: result.currency,
                    reference_id: result.reference_id
                });

                // Clear cart on both client and server
                console.log('🧹 Clearing cart...');
                localStorage.removeItem('cart');

                // Clear server-side cart
                try {
                    await fetch('/api/khqr/clear-cart', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    console.log('✅ Server cart cleared');
                } catch (error) {
                    console.error('❌ Error clearing server cart:', error);
                }

                // Check if we have invoice URL and order ID
                if (result.invoice_url && result.order_id) {
                    console.log(`🧾 Redirecting to invoice: ${result.invoice_url}`);
                    window.location.href = result.invoice_url;
                } else if (result.order_id) {
                    // Fallback: construct invoice URL manually
                    const invoiceUrl = `/invoice/${result.order_id}`;
                    console.log(`🧾 Constructing invoice URL: ${invoiceUrl}`);
                    window.location.href = invoiceUrl;
                } else {
                    // Fallback: show success message and redirect to homepage
                    console.log('⚠️ No order_id or invoice_url found, showing alert');
                    alert(`🎉 Payment Successful!\n\nAmount: ${result.currency || 'USD'} ${result.amount || 'N/A'}\nReference: ${result.reference_id || 'N/A'}\n\n💰 Money has been transferred!\n📦 Order processing...`);
                    window.location.href = '/';
                }
            });

            // Start KHQR payment with total amount
            window.khqrPayment.startPayment(totalPrice, 'USD', `CART_${Date.now()}`);
        }

        function selectCashPayment() {
            // Use global payment data instead of parsing from parameters
            const { cartItems, customerInfo, totalPrice } = currentPaymentData;

            closePaymentMethodModal();

            // Process cash payment
            processCashPayment(cartItems, customerInfo, totalPrice);
        }

        async function processCashPayment(cartItems, customerInfo, totalPrice) {
            try {
                // Show cash payment confirmation modal
                const confirmModal = document.createElement('div');
                confirmModal.id = 'cash-payment-modal';
                confirmModal.className = 'modal';
                confirmModal.style.cssText = `
                    display: block;
                    position: fixed;
                    z-index: 1002;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                `;

                confirmModal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px; margin: 10% auto;">
                        <h2 style="margin-bottom: 20px; color: #333; text-align: center;">💵 Cash Payment</h2>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h3 style="margin: 0 0 15px 0; color: #333;">Payment Details</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                <div><strong>Customer:</strong></div>
                                <div>${customerInfo.first_name} ${customerInfo.last_name}</div>
                                <div><strong>Total Amount:</strong></div>
                                <div style="font-size: 18px; font-weight: bold; color: #28a745;">$${totalPrice.toFixed(2)}</div>
                                <div><strong>Payment Method:</strong></div>
                                <div>Cash</div>
                            </div>
                        </div>

                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #856404; font-size: 14px;">
                                ⚠️ <strong>Staff Confirmation Required:</strong><br>
                                Please confirm that you have received the cash payment of <strong>$${totalPrice.toFixed(2)}</strong> from the customer before completing this order.
                            </p>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button onclick="cancelCashPayment()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer;">
                                Cancel
                            </button>
                            <button onclick="confirmCashPayment('${JSON.stringify(cartItems).replace(/"/g, '&quot;')}', '${JSON.stringify(customerInfo).replace(/"/g, '&quot;')}', ${totalPrice})"
                                    style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                                ✅ Confirm Cash Received
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(confirmModal);

            } catch (error) {
                console.error('Error processing cash payment:', error);
                alert('An error occurred while processing the cash payment.');
            }
        }

        function cancelCashPayment() {
            const modal = document.getElementById('cash-payment-modal');
            if (modal) {
                modal.remove();
            }
        }

        async function confirmCashPayment(cartItemsStr, customerInfoStr, totalPrice) {
            const cartItems = JSON.parse(cartItemsStr.replace(/&quot;/g, '"'));
            const customerInfo = JSON.parse(customerInfoStr.replace(/&quot;/g, '"'));

            try {
                // Create cash payment order
                const response = await fetch('/api/payment/cash', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        cart_items: cartItems,
                        customer_info: customerInfo,
                        total_amount: totalPrice
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Close cash payment modal
                    cancelCashPayment();

                    // Clear cart
                    await clearCartAPI();

                    // Reset customer form if it exists
                    if (customerForm) {
                        customerForm.reset();
                    }

                    // Handle different types of payments
                    if (data.payment_type === 'mixed_cart' && data.order_id && data.preorder_id) {
                        // Mixed cart payment - redirect to mixed cart summary
                        window.location.href = `/mixed-cart/summary/${data.order_id}/${data.preorder_id}`;
                    } else if (data.order_id) {
                        // Regular order - redirect to invoice
                        window.location.href = `/invoice/${data.order_id}`;
                    } else if (data.preorder_only && data.preorder_id) {
                        // Single pre-order payment - redirect to pre-order invoice
                        window.location.href = `/preorder/invoice/${data.preorder_id}`;
                    } else if (data.preorder_only) {
                        // Multiple pre-order payments - redirect to pre-orders page with success message
                        alert(data.message);
                        window.location.href = '/customer/preorders';
                    } else {
                        // Fallback - show success message and stay on page
                        alert(data.message);
                        window.location.reload();
                    }
                } else {
                    alert('Error processing cash payment: ' + data.error);
                }
            } catch (error) {
                console.error('Error confirming cash payment:', error);
                alert('An error occurred while processing the cash payment.');
            }
        }

        // Test KHQR Payment Function
        async function testKHQRPayment() {
            console.log('🧪 Testing KHQR payment flow...');

            const { cartItems, customerInfo, totalPrice } = currentPaymentData;

            closePaymentMethodModal();

            try {
                console.log('🧪 Creating test order via API...');

                // Call test endpoint to create real order
                const response = await fetch('/api/khqr/test-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: totalPrice
                    })
                });

                const result = await response.json();
                console.log('🧪 Test order API response:', result);

                if (result.success) {
                    // Clear cart
                    localStorage.removeItem('cart');

                    // Show success message
                    alert(`🧪 Test Order Created!\n\nOrder ID: ${result.order_id}\nAmount: ${result.currency} ${result.amount}\nReference: ${result.reference_id}`);

                    // Redirect to invoice
                    if (result.invoice_url) {
                        console.log(`🧾 Redirecting to test invoice: ${result.invoice_url}`);
                        window.location.href = result.invoice_url;
                    } else {
                        console.log('⚠️ No invoice URL in response');
                        window.location.href = '/';
                    }
                } else {
                    console.error('❌ Test order creation failed:', result.error);
                    alert(`❌ Test failed: ${result.error}`);
                }

            } catch (error) {
                console.error('❌ Error creating test order:', error);
                alert(`❌ Test error: ${error.message}`);
            }
        }

        // Make functions globally available
        window.showPaymentMethodSelection = showPaymentMethodSelection;
        window.closePaymentMethodModal = closePaymentMethodModal;
        window.selectKHQRPayment = selectKHQRPayment;
        window.selectCashPayment = selectCashPayment;
        window.cancelCashPayment = cancelCashPayment;
        window.confirmCashPayment = confirmCashPayment;
        window.testKHQRPayment = testKHQRPayment;


    </script>
</body>
</html>