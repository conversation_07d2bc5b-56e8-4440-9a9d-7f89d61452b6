/* Main Layout Styles */
body {
    padding-top: 56px; /* Account for fixed navbar */
}

/* Hero/Slider Section */
.ad-hero {
    width: 100%;
    height: 400px;
    overflow: hidden;
    position: relative;
    background: #f5f5f5;
    margin: 0 auto 2rem;
    border-radius: 0;
    box-shadow: none;
    border-bottom: 1px solid #e9ecef;
}

/* Section Spacing */
.section-spacer {
    margin: 2rem 0;
}

/* Category Section */
.category-section {
    padding: 2rem 0;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

/* Products Section */
.product-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.ad-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.ad-slide.active {
    opacity: 1;
}

.ad-nav {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
    z-index: 2;
}

.ad-nav-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ad-nav-btn.active {
    background: #fff;
    transform: scale(1.2);
}

/* Category Grid Section */
.grid-section {
    margin: 2rem auto;
    padding: 20px 0;
    position: relative;
    max-width: 1200px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    border-radius: 8px;
    background: white;
    margin: 0 auto;
    max-width: 1200px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.category-item {
    height: 180px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 1.5rem;
}

.category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    background: #e9ecef;
}

.category-item h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #212529;
}

.category-item i {
    color: #0d6efd;
    font-size: 2rem;
}

/* Product Cards */
.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 400px !important; /* Ensure minimum height for consistent layout */
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 200px !important;
    object-fit: contain;
    padding: 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    flex-shrink: 0 !important; /* Prevent image from shrinking */
}

.card-body {
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important; /* Take up remaining space */
    padding: 1rem !important;
    height: 100% !important;
    position: relative;
}

.card-text {
    flex-grow: 1 !important;
    color: #6c757d;
    margin-bottom: 1rem !important; /* Ensure space before buttons */
}

.price {
    color: #0d6efd;
    font-size: 1.1rem;
    font-weight: bold;
}

.add-to-cart {
    transition: all 0.3s;
}

.add-to-cart:hover {
    background-color: #0b5ed7 !important;
    color: white !important;
}

/* Button container for consistent positioning */
.card .d-grid {
    margin-top: auto !important; /* Push buttons to bottom of card */
    position: absolute !important;
    bottom: 1rem !important;
    left: 1rem !important;
    right: 1rem !important;
}

/* Ensure view product buttons are properly positioned */
.view-product-btn {
    width: 100% !important;
    margin-top: auto !important;
}

/* Add padding to card body to make room for absolute positioned buttons */
.card-body {
    padding-bottom: 4rem !important; /* Make room for buttons */
}

/* Product Grid Layout */
.product-grid {
    margin-top: 2rem;
}

/* Section Divider */
.section-divider {
    border-top: 2px solid #eee;
    margin: 3rem 0;
    position: relative;
}

.section-divider::after {
    content: "";
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 10px;
    background: #0d6efd;
    border-radius: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .ad-hero {
        height: 250px;
        margin: 0.5rem auto 2rem;
    }
    
    .category-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }
    
    .category-item {
        height: 140px;
        padding: 1rem;
    }
    
    .card-img-top {
        height: 160px;
        padding: 1rem;
    }
}

/* Featured Products Section */
.featured-products {
    padding: 2rem 0;
}

.featured-products h2 {
    margin-bottom: 2rem;
    font-weight: 600;
    color: #212529;
}
